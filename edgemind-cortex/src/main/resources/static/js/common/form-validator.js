/**
 * 统一表单验证器
 * 提供前端表单验证和后端验证结果处理的统一接口
 * 
 * <AUTHOR>
 * @version 1.0
 */
class FormValidator {
    constructor(formSelector, options = {}) {
        this.form = document.querySelector(formSelector);
        this.options = {
            // 是否启用实时验证
            realTimeValidation: true,
            // 是否显示加载状态
            showLoading: true,
            // 验证失败时的样式类
            errorClass: 'is-invalid',
            // 验证成功时的样式类
            successClass: 'is-valid',
            // 错误消息容器选择器
            errorContainer: '.invalid-feedback',
            // 提交按钮选择器
            submitButton: '[type="submit"]',
            // 后端验证API端点
            validationEndpoint: null,
            // 自定义验证规则
            customRules: {},
            ...options
        };
        
        this.validationRules = {
            required: {
                test: (value) => value !== null && value !== undefined && value.toString().trim() !== '',
                message: '此字段为必填项'
            },
            email: {
                test: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
                message: '请输入有效的邮箱地址'
            },
            phone: {
                test: (value) => /^1[3-9]\d{9}$/.test(value),
                message: '请输入有效的手机号码'
            },
            username: {
                test: (value) => /^[a-zA-Z0-9_]{3,20}$/.test(value),
                message: '用户名只能包含字母、数字和下划线，长度3-20位'
            },
            password: {
                test: (value) => /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/.test(value),
                message: '密码至少8位，包含大小写字母和数字'
            },
            minLength: (min) => ({
                test: (value) => value.length >= min,
                message: `最少需要${min}个字符`
            }),
            maxLength: (max) => ({
                test: (value) => value.length <= max,
                message: `最多允许${max}个字符`
            }),
            numeric: {
                test: (value) => /^\d+$/.test(value),
                message: '只能输入数字'
            },
            url: {
                test: (value) => /^https?:\/\/.+/.test(value),
                message: '请输入有效的URL地址'
            }
        };
        
        // 合并自定义验证规则
        Object.assign(this.validationRules, this.options.customRules);
        
        this.init();
    }
    
    init() {
        if (!this.form) {
            console.error('表单元素未找到');
            return;
        }
        
        // 绑定表单提交事件
        this.form.addEventListener('submit', this.handleSubmit.bind(this));
        
        // 绑定实时验证
        if (this.options.realTimeValidation) {
            this.bindRealTimeValidation();
        }
    }
    
    bindRealTimeValidation() {
        const inputs = this.form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.clearFieldError(input));
        });
    }
    
    async handleSubmit(event) {
        event.preventDefault();
        
        // 前端验证
        const frontendValid = this.validateForm();
        if (!frontendValid) {
            return false;
        }
        
        // 显示加载状态
        if (this.options.showLoading) {
            this.showLoading(true);
        }
        
        try {
            // 后端验证（如果配置了验证端点）
            if (this.options.validationEndpoint) {
                const backendValid = await this.validateOnServer();
                if (!backendValid) {
                    return false;
                }
            }
            
            // 验证通过，触发自定义提交事件
            const submitEvent = new CustomEvent('formValidated', {
                detail: { formData: new FormData(this.form) }
            });
            this.form.dispatchEvent(submitEvent);
            
        } catch (error) {
            console.error('表单验证失败:', error);
            this.showGlobalError('验证过程中发生错误，请重试');
        } finally {
            if (this.options.showLoading) {
                this.showLoading(false);
            }
        }
    }
    
    validateForm() {
        let isValid = true;
        const inputs = this.form.querySelectorAll('[data-validate]');
        
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    validateField(field) {
        const rules = field.dataset.validate.split('|');
        const value = field.value;
        
        // 清除之前的错误状态
        this.clearFieldError(field);
        
        for (const rule of rules) {
            const [ruleName, ...params] = rule.split(':');
            const validationRule = this.getValidationRule(ruleName, params);
            
            if (validationRule && !validationRule.test(value)) {
                this.showFieldError(field, validationRule.message);
                return false;
            }
        }
        
        this.showFieldSuccess(field);
        return true;
    }
    
    getValidationRule(ruleName, params) {
        if (typeof this.validationRules[ruleName] === 'function') {
            return this.validationRules[ruleName](...params);
        }
        return this.validationRules[ruleName];
    }
    
    showFieldError(field, message) {
        field.classList.remove(this.options.successClass);
        field.classList.add(this.options.errorClass);
        
        const errorContainer = field.parentNode.querySelector(this.options.errorContainer);
        if (errorContainer) {
            errorContainer.textContent = message;
            errorContainer.style.display = 'block';
        }
    }
    
    showFieldSuccess(field) {
        field.classList.remove(this.options.errorClass);
        field.classList.add(this.options.successClass);
        
        const errorContainer = field.parentNode.querySelector(this.options.errorContainer);
        if (errorContainer) {
            errorContainer.style.display = 'none';
        }
    }
    
    clearFieldError(field) {
        field.classList.remove(this.options.errorClass, this.options.successClass);
        
        const errorContainer = field.parentNode.querySelector(this.options.errorContainer);
        if (errorContainer) {
            errorContainer.style.display = 'none';
        }
    }
    
    async validateOnServer() {
        try {
            const formData = new FormData(this.form);
            const response = await fetch(this.options.validationEndpoint, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                return true;
            } else {
                // 处理后端验证错误
                this.handleServerValidationErrors(result.errors || {});
                return false;
            }
        } catch (error) {
            console.error('服务器验证失败:', error);
            this.showGlobalError('服务器验证失败，请重试');
            return false;
        }
    }
    
    handleServerValidationErrors(errors) {
        Object.keys(errors).forEach(fieldName => {
            const field = this.form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                this.showFieldError(field, errors[fieldName]);
            }
        });
    }
    
    showLoading(show) {
        const submitButton = this.form.querySelector(this.options.submitButton);
        if (submitButton) {
            if (show) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 验证中...';
            } else {
                submitButton.disabled = false;
                submitButton.innerHTML = submitButton.dataset.originalText || '提交';
            }
        }
    }
    
    showGlobalError(message) {
        // 显示全局错误消息
        const alertHtml = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle"></i> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        const alertContainer = this.form.querySelector('.alert-container') || this.form;
        alertContainer.insertAdjacentHTML('afterbegin', alertHtml);
    }
    
    // 重置表单验证状态
    reset() {
        const inputs = this.form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => this.clearFieldError(input));
        
        // 清除全局错误消息
        const alerts = this.form.querySelectorAll('.alert');
        alerts.forEach(alert => alert.remove());
    }
    
    // 手动触发字段验证
    validateFieldByName(fieldName) {
        const field = this.form.querySelector(`[name="${fieldName}"]`);
        if (field) {
            return this.validateField(field);
        }
        return false;
    }
    
    // 设置字段值并验证
    setFieldValue(fieldName, value) {
        const field = this.form.querySelector(`[name="${fieldName}"]`);
        if (field) {
            field.value = value;
            this.validateField(field);
        }
    }
}

// 导出为全局变量
window.FormValidator = FormValidator;
