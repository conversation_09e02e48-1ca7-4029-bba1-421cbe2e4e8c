/**
 * 权限管理工具类
 * 提供前后端双重权限验证和实时权限状态管理
 */
class PermissionManager {
    constructor() {
        this.permissions = new Set();
        this.roles = new Set();
        this.permissionCache = new Map();
        this.cacheExpireTime = 5 * 60 * 1000; // 5分钟缓存
        this.lastUpdateTime = 0;
        this.permissionChangeListeners = [];
        
        // 初始化权限数据
        this.initPermissions();
        
        // 设置定期权限检查
        this.setupPeriodicCheck();
    }
    
    /**
     * 初始化权限数据
     */
    async initPermissions() {
        try {
            const response = await fetch(`${CONTEXT_PATH}/api/auth/current-permissions`, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + this.getToken(),
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                if (data.code === 200) {
                    this.updatePermissions(data.data.permissions, data.data.roles);
                    this.lastUpdateTime = Date.now();
                }
            } else if (response.status === 401) {
                // Token过期，重定向到登录页
                this.handleUnauthorized();
            }
        } catch (error) {
            console.error('初始化权限失败:', error);
        }
    }
    
    /**
     * 更新权限数据
     */
    updatePermissions(permissions, roles) {
        const oldPermissions = new Set(this.permissions);
        const oldRoles = new Set(this.roles);
        
        this.permissions = new Set(permissions || []);
        this.roles = new Set(roles || []);
        
        // 检查权限是否发生变化
        const permissionsChanged = !this.setsEqual(oldPermissions, this.permissions);
        const rolesChanged = !this.setsEqual(oldRoles, this.roles);
        
        if (permissionsChanged || rolesChanged) {
            this.notifyPermissionChange();
        }
    }
    
    /**
     * 检查是否有指定权限（前端检查）
     */
    hasPermission(permission) {
        if (!permission) return true;
        
        // 前端快速检查
        const hasLocal = this.permissions.has(permission);
        
        // 记录权限检查日志（用于审计）
        this.logPermissionCheck(permission, hasLocal);
        
        return hasLocal;
    }
    
    /**
     * 检查是否有指定角色
     */
    hasRole(role) {
        if (!role) return true;
        return this.roles.has(role);
    }
    
    /**
     * 检查是否有任一权限
     */
    hasAnyPermission(permissions) {
        if (!permissions || permissions.length === 0) return true;
        return permissions.some(permission => this.hasPermission(permission));
    }
    
    /**
     * 检查是否有所有权限
     */
    hasAllPermissions(permissions) {
        if (!permissions || permissions.length === 0) return true;
        return permissions.every(permission => this.hasPermission(permission));
    }
    
    /**
     * 后端权限验证（关键操作必须调用）
     */
    async validatePermissionOnServer(permission, action = 'access') {
        try {
            const cacheKey = `${permission}_${action}`;
            const cached = this.permissionCache.get(cacheKey);
            
            // 检查缓存
            if (cached && (Date.now() - cached.timestamp) < this.cacheExpireTime) {
                return cached.result;
            }
            
            const response = await fetch(`${CONTEXT_PATH}/api/auth/validate-permission`, {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + this.getToken(),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    permission: permission,
                    action: action,
                    timestamp: Date.now()
                })
            });
            
            if (response.status === 401) {
                this.handleUnauthorized();
                return false;
            }
            
            const data = await response.json();
            const result = data.code === 200 && data.data === true;
            
            // 缓存结果
            this.permissionCache.set(cacheKey, {
                result: result,
                timestamp: Date.now()
            });
            
            return result;
        } catch (error) {
            console.error('服务器权限验证失败:', error);
            return false;
        }
    }
    
    /**
     * 执行需要权限的操作
     */
    async executeWithPermission(permission, operation, options = {}) {
        const { 
            showLoading = true, 
            fallbackMessage = '您没有执行此操作的权限',
            skipServerValidation = false 
        } = options;
        
        // 前端快速检查
        if (!this.hasPermission(permission)) {
            EdgeMind.showToast(fallbackMessage, 'warning');
            return false;
        }
        
        // 关键操作需要服务器验证
        if (!skipServerValidation) {
            if (showLoading) {
                EdgeMind.showLoading('验证权限中...');
            }
            
            const hasServerPermission = await this.validatePermissionOnServer(permission);
            
            if (showLoading) {
                EdgeMind.hideLoading();
            }
            
            if (!hasServerPermission) {
                EdgeMind.showToast('权限验证失败，请刷新页面重试', 'error');
                // 刷新权限数据
                await this.refreshPermissions();
                return false;
            }
        }
        
        // 执行操作
        try {
            return await operation();
        } catch (error) {
            if (error.status === 403) {
                EdgeMind.showToast('权限不足，无法执行此操作', 'error');
                await this.refreshPermissions();
            }
            throw error;
        }
    }
    
    /**
     * 刷新权限数据
     */
    async refreshPermissions() {
        this.permissionCache.clear();
        await this.initPermissions();
    }
    
    /**
     * 设置定期权限检查
     */
    setupPeriodicCheck() {
        // 每5分钟检查一次权限状态
        setInterval(async () => {
            if (Date.now() - this.lastUpdateTime > this.cacheExpireTime) {
                await this.refreshPermissions();
            }
        }, this.cacheExpireTime);
        
        // 页面获得焦点时检查权限
        window.addEventListener('focus', () => {
            if (Date.now() - this.lastUpdateTime > 60000) { // 1分钟
                this.refreshPermissions();
            }
        });
    }
    
    /**
     * 添加权限变更监听器
     */
    addPermissionChangeListener(listener) {
        this.permissionChangeListeners.push(listener);
    }
    
    /**
     * 移除权限变更监听器
     */
    removePermissionChangeListener(listener) {
        const index = this.permissionChangeListeners.indexOf(listener);
        if (index > -1) {
            this.permissionChangeListeners.splice(index, 1);
        }
    }
    
    /**
     * 通知权限变更
     */
    notifyPermissionChange() {
        console.log('权限发生变更，通知相关组件更新');
        
        this.permissionChangeListeners.forEach(listener => {
            try {
                listener({
                    permissions: Array.from(this.permissions),
                    roles: Array.from(this.roles),
                    timestamp: Date.now()
                });
            } catch (error) {
                console.error('权限变更监听器执行失败:', error);
            }
        });
        
        // 触发页面权限状态更新
        this.updatePagePermissionState();
    }
    
    /**
     * 更新页面权限状态
     */
    updatePagePermissionState() {
        // 更新所有带权限控制的元素
        document.querySelectorAll('[data-permission]').forEach(element => {
            const permission = element.getAttribute('data-permission');
            const hasPermission = this.hasPermission(permission);
            
            if (hasPermission) {
                element.style.display = '';
                element.removeAttribute('disabled');
            } else {
                element.style.display = 'none';
                element.setAttribute('disabled', 'disabled');
            }
        });
        
        // 更新角色控制的元素
        document.querySelectorAll('[data-role]').forEach(element => {
            const role = element.getAttribute('data-role');
            const hasRole = this.hasRole(role);
            
            if (hasRole) {
                element.style.display = '';
                element.removeAttribute('disabled');
            } else {
                element.style.display = 'none';
                element.setAttribute('disabled', 'disabled');
            }
        });
    }
    
    /**
     * 工具方法
     */
    setsEqual(set1, set2) {
        if (set1.size !== set2.size) return false;
        for (let item of set1) {
            if (!set2.has(item)) return false;
        }
        return true;
    }
    
    getToken() {
        return localStorage.getItem('token') || sessionStorage.getItem('token') || '';
    }
    
    handleUnauthorized() {
        localStorage.removeItem('token');
        sessionStorage.removeItem('token');
        window.location.href = `${CONTEXT_PATH}/login`;
    }
    
    logPermissionCheck(permission, result) {
        // 可以发送到服务器进行审计
        console.debug(`权限检查: ${permission} = ${result}`);
    }
}

// 创建全局权限管理器实例
window.PermissionManager = new PermissionManager();

// 导出权限检查函数供全局使用
window.hasPermission = (permission) => window.PermissionManager.hasPermission(permission);
window.hasRole = (role) => window.PermissionManager.hasRole(role);
window.executeWithPermission = (permission, operation, options) => 
    window.PermissionManager.executeWithPermission(permission, operation, options);
