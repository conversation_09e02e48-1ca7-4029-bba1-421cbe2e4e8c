/**
 * 登录页面JavaScript - EdgeMind设计系统
 */

// 获取上下文路径
const CONTEXT_PATH = window.location.pathname.startsWith('/wkg') ? '/wkg' : '';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initLoginPage();
});

/**
 * 初始化登录页面
 */
function initLoginPage() {
    bindEvents();
    checkAutoLogin();
    focusFirstInput();
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 登录表单提交
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
    
    // 密码显示/隐藏切换
    document.getElementById('togglePassword').addEventListener('click', togglePasswordVisibility);
    
    // 输入框实时验证
    document.getElementById('username').addEventListener('input', validateUsername);
    document.getElementById('password').addEventListener('input', validatePassword);
    
    // 回车键登录
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleLogin(e);
        }
    });
}

/**
 * 处理登录
 */
async function handleLogin(e) {
    e.preventDefault();
    
    // 表单验证
    if (!validateForm()) {
        return;
    }
    
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('rememberMe').checked;
    
    // 显示加载状态
    setLoginLoading(true);
    hideAlerts();
    
    try {
        const response = await fetch(`${CONTEXT_PATH}/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                username: username,
                password: password,
                rememberMe: rememberMe
            }),
            credentials: 'include'
        });
        
        const data = await response.json();
        
        if (data.code === 200) {
            // 登录成功
            showSuccess('登录成功，正在跳转...');
            
            // 保存登录状态
            if (rememberMe) {
                localStorage.setItem('edgemind_remember', 'true');
                localStorage.setItem('edgemind_username', username);
            }
            
            // 延迟跳转，让用户看到成功提示
            setTimeout(() => {
                // 跳转到原始请求页面或默认首页
                const redirectUrl = getRedirectUrl() || `${CONTEXT_PATH}/system/user/content`;
                window.location.href = redirectUrl;
            }, 1000);
            
        } else {
            // 登录失败
            showError(data.message || '登录失败，请检查用户名和密码');
            
            // 增加失败次数记录
            incrementFailedAttempts();
        }
        
    } catch (error) {
        console.error('Login error:', error);
        showError('网络错误，请稍后重试');
    } finally {
        setLoginLoading(false);
    }
}

/**
 * 表单验证
 */
function validateForm() {
    const usernameValid = validateUsername();
    const passwordValid = validatePassword();
    
    return usernameValid && passwordValid;
}

/**
 * 验证用户名
 */
function validateUsername() {
    const username = document.getElementById('username').value.trim();
    const usernameInput = document.getElementById('username');
    const errorElement = document.getElementById('usernameError');
    
    if (!username) {
        setFieldError(usernameInput, errorElement, '请输入用户名');
        return false;
    }
    
    if (username.length < 2) {
        setFieldError(usernameInput, errorElement, '用户名至少2个字符');
        return false;
    }
    
    if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(username)) {
        setFieldError(usernameInput, errorElement, '用户名只能包含字母、数字、下划线和中文');
        return false;
    }
    
    setFieldValid(usernameInput, errorElement);
    return true;
}

/**
 * 验证密码
 */
function validatePassword() {
    const password = document.getElementById('password').value;
    const passwordInput = document.getElementById('password');
    const errorElement = document.getElementById('passwordError');
    
    if (!password) {
        setFieldError(passwordInput, errorElement, '请输入密码');
        return false;
    }
    
    if (password.length < 6) {
        setFieldError(passwordInput, errorElement, '密码至少6个字符');
        return false;
    }
    
    setFieldValid(passwordInput, errorElement);
    return true;
}

/**
 * 设置字段错误状态
 */
function setFieldError(input, errorElement, message) {
    input.classList.add('is-invalid');
    input.classList.remove('is-valid');
    errorElement.textContent = message;
    errorElement.style.display = 'block';
}

/**
 * 设置字段有效状态
 */
function setFieldValid(input, errorElement) {
    input.classList.remove('is-invalid');
    input.classList.add('is-valid');
    errorElement.style.display = 'none';
}

/**
 * 切换密码显示/隐藏
 */
function togglePasswordVisibility() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('togglePasswordIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'bi bi-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'bi bi-eye';
    }
}

/**
 * 设置登录加载状态
 */
function setLoginLoading(loading) {
    const loginBtn = document.getElementById('loginBtn');
    const btnText = loginBtn.querySelector('.btn-text');
    const spinner = document.getElementById('loadingSpinner');
    
    if (loading) {
        loginBtn.disabled = true;
        btnText.textContent = '登录中...';
        spinner.style.display = 'inline-block';
    } else {
        loginBtn.disabled = false;
        btnText.textContent = '登录';
        spinner.style.display = 'none';
    }
}

/**
 * 显示错误提示
 */
function showError(message) {
    const errorAlert = document.getElementById('errorAlert');
    const errorMessage = document.getElementById('errorMessage');
    
    errorMessage.textContent = message;
    errorAlert.style.display = 'block';
    
    // 3秒后自动隐藏
    setTimeout(() => {
        errorAlert.style.display = 'none';
    }, 5000);
}

/**
 * 显示成功提示
 */
function showSuccess(message) {
    const successAlert = document.getElementById('successAlert');
    const successMessage = document.getElementById('successMessage');
    
    successMessage.textContent = message;
    successAlert.style.display = 'block';
}

/**
 * 隐藏所有提示
 */
function hideAlerts() {
    document.getElementById('errorAlert').style.display = 'none';
    document.getElementById('successAlert').style.display = 'none';
}

/**
 * 获取重定向URL
 */
function getRedirectUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('redirect');
}

/**
 * 检查自动登录
 */
function checkAutoLogin() {
    const remember = localStorage.getItem('edgemind_remember');
    const savedUsername = localStorage.getItem('edgemind_username');
    
    if (remember === 'true' && savedUsername) {
        document.getElementById('username').value = savedUsername;
        document.getElementById('rememberMe').checked = true;
    }
}

/**
 * 聚焦第一个输入框
 */
function focusFirstInput() {
    const usernameInput = document.getElementById('username');
    if (!usernameInput.value) {
        usernameInput.focus();
    } else {
        document.getElementById('password').focus();
    }
}

/**
 * 增加失败次数记录
 */
function incrementFailedAttempts() {
    const attempts = parseInt(sessionStorage.getItem('login_attempts') || '0') + 1;
    sessionStorage.setItem('login_attempts', attempts.toString());
    
    // 失败次数过多时显示警告
    if (attempts >= 3) {
        showError(`登录失败${attempts}次，请注意账户安全`);
    }
}

/**
 * 清除失败次数记录
 */
function clearFailedAttempts() {
    sessionStorage.removeItem('login_attempts');
}
