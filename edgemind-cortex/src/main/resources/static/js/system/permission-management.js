/**
 * 权限查看页面JavaScript - 只读模式
 */

// 全局变量
let permissionTree = [];
let allPermissions = [];
let filteredPermissions = [];

// 获取上下文路径
const CONTEXT_PATH = window.location.pathname.startsWith('/wkg') ? '/wkg' : '';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initPage();
});

/**
 * 初始化页面
 */
function initPage() {
    loadPermissionTree();
    bindEvents();
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 搜索功能
    document.getElementById('searchPermissionName').addEventListener('input', debounce(searchPermissions, 300));
    document.getElementById('searchPermissionType').addEventListener('change', searchPermissions);
}

/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 统一的API请求方法
 */
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'include'
    };

    const finalOptions = { ...defaultOptions, ...options };

    try {
        const response = await fetch(`${CONTEXT_PATH}${url}`, finalOptions);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.code !== 200) {
            throw new Error(data.message || '操作失败');
        }

        return data;
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

/**
 * 显示Toast提示
 */
function showToast(message, type = 'info') {
    if (typeof window.showToast === 'function') {
        window.showToast(message, type);
    } else {
        alert(`${type.toUpperCase()}: ${message}`);
    }
}

/**
 * 加载权限树
 */
async function loadPermissionTree() {
    try {
        const data = await apiRequest('/api/system/permission/tree');
        permissionTree = data.data || [];
        allPermissions = flattenPermissionTree(permissionTree);
        filteredPermissions = [...allPermissions];
        renderPermissionTree(permissionTree);
    } catch (error) {
        showToast('加载权限树失败: ' + error.message, 'danger');
        // 显示空状态
        document.getElementById('permissionTreeContainer').innerHTML = `
            <div class="text-center text-muted py-5">
                <i class="bi bi-exclamation-triangle" style="font-size: 3rem;"></i>
                <p class="mt-2">权限数据加载失败</p>
            </div>
        `;
    }
}

/**
 * 扁平化权限树
 */
function flattenPermissionTree(nodes, result = []) {
    nodes.forEach(node => {
        result.push(node);
        if (node.children && node.children.length > 0) {
            flattenPermissionTree(node.children, result);
        }
    });
    return result;
}

/**
 * 搜索权限
 */
function searchPermissions() {
    const nameKeyword = document.getElementById('searchPermissionName').value.toLowerCase();
    const typeFilter = document.getElementById('searchPermissionType').value;

    filteredPermissions = allPermissions.filter(permission => {
        const nameMatch = !nameKeyword || permission.name.toLowerCase().includes(nameKeyword);
        const typeMatch = !typeFilter || permission.type === typeFilter;
        return nameMatch && typeMatch;
    });

    // 重新构建过滤后的树结构
    const filteredTree = buildFilteredTree(filteredPermissions);
    renderPermissionTree(filteredTree);
}

/**
 * 重置搜索
 */
function resetSearch() {
    document.getElementById('searchPermissionName').value = '';
    document.getElementById('searchPermissionType').value = '';
    filteredPermissions = [...allPermissions];
    renderPermissionTree(permissionTree);
}

/**
 * 构建过滤后的树结构
 */
function buildFilteredTree(filteredPermissions) {
    if (filteredPermissions.length === allPermissions.length) {
        return permissionTree;
    }

    const filteredIds = new Set(filteredPermissions.map(p => p.id));

    function filterNode(node) {
        if (filteredIds.has(node.id)) {
            const filteredNode = { ...node };
            if (node.children) {
                filteredNode.children = node.children
                    .map(child => filterNode(child))
                    .filter(child => child !== null);
            }
            return filteredNode;
        }
        return null;
    }

    return permissionTree
        .map(node => filterNode(node))
        .filter(node => node !== null);
}

/**
 * 渲染权限树
 */
function renderPermissionTree(nodes, level = 0) {
    const container = document.getElementById('permissionTreeContainer');
    if (level === 0) {
        container.innerHTML = '';
    }
    
    nodes.forEach(node => {
        const div = document.createElement('div');
        div.className = `permission-node level-${level}`;
        div.setAttribute('data-permission-id', node.id);
        
        const hasChildren = node.children && node.children.length > 0;
        const expandIcon = hasChildren ? 
            `<i class="bi bi-chevron-right expand-icon me-2" onclick="toggleNode(this)" style="cursor: pointer;"></i>` : 
            `<span class="me-4"></span>`;
        
        div.innerHTML = `
            <div class="d-flex align-items-center p-2 rounded"
                 style="cursor: pointer; transition: background-color 0.2s; margin-left: ${level * 20}px;"
                 onclick="selectPermission(${node.id})"
                 onmouseover="this.style.backgroundColor='var(--hover-bg)'"
                 onmouseout="this.style.backgroundColor='transparent'">
                ${expandIcon}
                ${node.icon ? `<i class="${node.icon} me-2" style="color: var(--primary-color);"></i>` : ''}
                <strong style="color: var(--text-primary);">${node.permissionName || node.name}</strong>
                <span class="badge ${getPermissionTypeBadgeClass(node.type)} ms-2" style="font-size: 0.7rem;">
                    ${getPermissionTypeText(node.type)}
                </span>
                <small class="text-muted ms-2">${node.permissionCode || node.code}</small>
            </div>
        `;
        
        container.appendChild(div);
        
        if (hasChildren) {
            const childContainer = document.createElement('div');
            childContainer.className = 'permission-children';
            childContainer.style.display = 'none';
            container.appendChild(childContainer);
            
            // 递归渲染子节点
            renderPermissionTreeChildren(node.children, childContainer, level + 1);
        }
    });
}

/**
 * 渲染权限树子节点
 */
function renderPermissionTreeChildren(nodes, container, level) {
    nodes.forEach(node => {
        const div = document.createElement('div');
        div.className = `permission-node level-${level}`;
        div.setAttribute('data-permission-id', node.id);
        
        const hasChildren = node.children && node.children.length > 0;
        const expandIcon = hasChildren ? 
            `<i class="bi bi-chevron-right expand-icon me-2" onclick="toggleNode(this)" style="cursor: pointer;"></i>` : 
            `<span class="me-4"></span>`;
        
        div.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center" onclick="selectPermission(${node.id})">
                    ${expandIcon}
                    ${node.icon ? `<i class="${node.icon} me-2"></i>` : ''}
                    <strong>${node.permissionName}</strong>
                    <span class="badge permission-type-badge ${getPermissionTypeBadgeClass(node.type)} ms-2">
                        ${getPermissionTypeText(node.type)}
                    </span>
                    <small class="text-muted ms-2">${node.permissionCode}</small>
                </div>
                <div class="permission-actions">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="editPermission(${node.id})" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="addChildPermission(${node.id})" title="添加子权限">
                        <i class="bi bi-plus"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="deletePermission(${node.id})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;
        
        container.appendChild(div);
        
        if (hasChildren) {
            const childContainer = document.createElement('div');
            childContainer.className = 'permission-children';
            childContainer.style.display = 'none';
            container.appendChild(childContainer);
            
            // 递归渲染子节点
            renderPermissionTreeChildren(node.children, childContainer, level + 1);
        }
    });
}

/**
 * 切换节点展开/收起
 */
function toggleNode(icon) {
    const isExpanded = icon.classList.contains('bi-chevron-down');
    const childContainer = icon.closest('.permission-node').nextElementSibling;
    
    if (isExpanded) {
        icon.classList.remove('bi-chevron-down');
        icon.classList.add('bi-chevron-right');
        if (childContainer && childContainer.classList.contains('permission-children')) {
            childContainer.style.display = 'none';
        }
    } else {
        icon.classList.remove('bi-chevron-right');
        icon.classList.add('bi-chevron-down');
        if (childContainer && childContainer.classList.contains('permission-children')) {
            childContainer.style.display = 'block';
        }
    }
}

/**
 * 展开全部节点
 */
function expandAll() {
    const expandIcons = document.querySelectorAll('.expand-icon.bi-chevron-right');
    expandIcons.forEach(icon => {
        toggleNode(icon);
    });
}

/**
 * 收起全部节点
 */
function collapseAll() {
    const expandIcons = document.querySelectorAll('.expand-icon.bi-chevron-down');
    expandIcons.forEach(icon => {
        toggleNode(icon);
    });
}

/**
 * 选择权限
 */
function selectPermission(permissionId) {
    // 移除之前的选中状态
    document.querySelectorAll('.permission-node').forEach(node => {
        node.classList.remove('border-primary', 'bg-primary', 'bg-opacity-10');
    });
    
    // 添加选中状态
    const selectedNode = document.querySelector(`[data-permission-id="${permissionId}"]`);
    if (selectedNode) {
        selectedNode.classList.add('border-primary', 'bg-primary', 'bg-opacity-10');
    }
    
    // 加载权限详情
    loadPermissionDetail(permissionId);
}

/**
 * 加载权限详情
 */
function loadPermissionDetail(permissionId) {
    fetch(`/wkg/api/system/permission/${permissionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderPermissionDetail(data.data);
            } else {
                showAlert('加载权限详情失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('网络错误，请稍后重试', 'danger');
        });
}

/**
 * 渲染权限详情
 */
function renderPermissionDetail(permission) {
    const detailContainer = document.getElementById('permissionDetail');
    detailContainer.innerHTML = `
        <div class="mb-3">
            <label class="form-label fw-bold">权限名称</label>
            <div class="form-control-plaintext">${permission.permissionName}</div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">权限编码</label>
            <div class="form-control-plaintext"><code>${permission.permissionCode}</code></div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">权限类型</label>
            <div class="form-control-plaintext">
                <span class="badge ${getPermissionTypeBadgeClass(permission.type)}">
                    ${getPermissionTypeText(permission.type)}
                </span>
            </div>
        </div>
        ${permission.resourceUrl ? `
        <div class="mb-3">
            <label class="form-label fw-bold">资源URL</label>
            <div class="form-control-plaintext"><code>${permission.resourceUrl}</code></div>
        </div>
        ` : ''}
        ${permission.httpMethod ? `
        <div class="mb-3">
            <label class="form-label fw-bold">HTTP方法</label>
            <div class="form-control-plaintext"><span class="badge bg-info">${permission.httpMethod}</span></div>
        </div>
        ` : ''}
        <div class="mb-3">
            <label class="form-label fw-bold">状态</label>
            <div class="form-control-plaintext">
                <span class="badge ${permission.status === 1 ? 'bg-success' : 'bg-danger'}">
                    ${permission.status === 1 ? '启用' : '禁用'}
                </span>
            </div>
        </div>
        ${permission.description ? `
        <div class="mb-3">
            <label class="form-label fw-bold">描述</label>
            <div class="form-control-plaintext">${permission.description}</div>
        </div>
        ` : ''}
        <div class="mb-3">
            <label class="form-label fw-bold">创建时间</label>
            <div class="form-control-plaintext">${formatDateTime(permission.createTime)}</div>
        </div>
        <div class="mb-3">
            <label class="form-label fw-bold">更新时间</label>
            <div class="form-control-plaintext">${formatDateTime(permission.updateTime)}</div>
        </div>
    `;
}

/**
 * 获取权限类型对应的徽章样式
 */
function getPermissionTypeBadgeClass(type) {
    const typeMap = {
        'MENU': 'bg-primary',
        'API': 'bg-success',
        'BUTTON': 'bg-warning',
        'DATA': 'bg-info'
    };
    return typeMap[type] || 'bg-secondary';
}

/**
 * 获取权限类型文本
 */
function getPermissionTypeText(type) {
    const typeMap = {
        'MENU': '菜单',
        'API': '接口',
        'BUTTON': '按钮',
        'DATA': '数据'
    };
    return typeMap[type] || type;
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}

// 所有编辑功能已移除，此页面仅用于权限查看

// 权限查看页面 - 所有编辑功能已移除
