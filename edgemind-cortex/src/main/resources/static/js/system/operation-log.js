/**
 * 操作日志页面JavaScript
 */

// 全局变量
let currentPage = 1;
let pageSize = 10;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initPage();
});

/**
 * 初始化页面
 */
function initPage() {
    loadLogList();
    loadStatistics();
    bindEvents();
    setDefaultTimeRange();
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 搜索表单提交
    document.getElementById('searchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        currentPage = 1;
        loadLogList();
    });

    // 保留天数输入框变化
    document.getElementById('retentionDays').addEventListener('input', function() {
        const days = this.value;
        document.querySelector('#cleanLogModal .form-text span').textContent = days;
    });
}

/**
 * 设置默认时间范围（最近7天）
 */
function setDefaultTimeRange() {
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    document.getElementById('searchStartTime').value = formatDateTimeForInput(weekAgo);
    document.getElementById('searchEndTime').value = formatDateTimeForInput(now);
}

/**
 * 加载操作日志列表
 */
async function loadLogList() {
    try {
        const searchParams = {
            pageNum: currentPage,
            pageSize: pageSize,
            userId: null, // 可以根据用户名查询用户ID
            operationType: document.getElementById('searchOperationType').value,
            module: document.getElementById('searchModule').value,
            startTime: document.getElementById('searchStartTime').value,
            endTime: document.getElementById('searchEndTime').value
        };

        // 移除空值参数
        Object.keys(searchParams).forEach(key => {
            if (!searchParams[key]) {
                delete searchParams[key];
            }
        });

        const data = await apiClient.get('/system/operation-log/list', searchParams);
        renderLogTable(data.data.records || []);
        renderPagination(data.data, 'pagination', changePage);
    } catch (error) {
        showError('加载操作日志失败: ' + error.message);
    }
}

/**
 * 渲染日志表格
 */
function renderLogTable(logs) {
    const tbody = document.getElementById('logTableBody');
    tbody.innerHTML = '';

    logs.forEach(log => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${formatDateTime(log.createTime)}</td>
            <td>${log.username || '-'}</td>
            <td>
                <span class="badge operation-type-badge ${getOperationTypeBadgeClass(log.operationType)}">
                    ${getOperationTypeText(log.operationType)}
                </span>
            </td>
            <td>${log.module}</td>
            <td class="log-detail" title="${log.description}">${log.description}</td>
            <td>${log.ipAddress || '-'}</td>
            <td>${log.executionTime ? log.executionTime + 'ms' : '-'}</td>
            <td>
                <span class="badge status-badge ${log.status === 1 ? 'bg-success' : 'bg-danger'}">
                    ${log.status === 1 ? '成功' : '失败'}
                </span>
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-info" onclick="showLogDetail(${log.id})" title="查看详情">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 分页渲染已移至通用库中的renderPagination函数

/**
 * 切换页面
 */
function changePage(page) {
    if (page < 1) return;
    currentPage = page;
    loadLogList();
}

/**
 * 加载统计数据
 */
async function loadStatistics() {
    try {
        const data = await apiClient.get('/system/operation-log/statistics', { days: 7 });
        const stats = data.data;

        document.getElementById('totalOperations').textContent = stats.totalOperations || 0;
        document.getElementById('todayOperations').textContent = stats.todayOperations || 0;

        // 计算成功率
        const successRate = stats.totalOperations > 0 ?
            Math.round((stats.todayOperations / stats.totalOperations) * 100) : 0;
        document.getElementById('successRate').textContent = successRate + '%';

        // 活跃用户数（这里需要额外的API）
        document.getElementById('activeUsers').textContent = '-';
    } catch (error) {
        console.error('Error loading statistics:', error);
    }
}

/**
 * 刷新统计数据
 */
function refreshStatistics() {
    loadStatistics();
    showAlert('统计数据已刷新', 'success');
}

/**
 * 显示日志详情
 */
function showLogDetail(logId) {
    // 从当前表格中找到对应的日志记录
    const logs = Array.from(document.querySelectorAll('#logTableBody tr')).map(row => {
        const cells = row.cells;
        return {
            createTime: cells[0].textContent,
            username: cells[1].textContent,
            operationType: cells[2].textContent.trim(),
            module: cells[3].textContent,
            description: cells[4].getAttribute('title'),
            ipAddress: cells[5].textContent,
            executionTime: cells[6].textContent,
            status: cells[7].textContent.trim()
        };
    });
    
    // 这里应该通过API获取完整的日志详情
    // 为了演示，我们使用模拟数据
    const log = {
        username: '演示用户',
        createTime: new Date().toLocaleString(),
        operationType: 'CREATE',
        module: '用户管理',
        description: '创建用户',
        requestUrl: '/api/system/user',
        requestMethod: 'POST',
        ipAddress: '*************',
        executionTime: '125ms',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        requestParams: '{"username":"testuser","email":"<EMAIL>"}',
        responseResult: '{"success":true,"data":{"id":123}}',
        errorMessage: null
    };
    
    // 填充模态框数据
    document.getElementById('detailUsername').textContent = log.username || '-';
    document.getElementById('detailCreateTime').textContent = log.createTime || '-';
    document.getElementById('detailOperationType').textContent = getOperationTypeText(log.operationType);
    document.getElementById('detailModule').textContent = log.module || '-';
    document.getElementById('detailDescription').textContent = log.description || '-';
    document.getElementById('detailRequestUrl').textContent = log.requestUrl || '-';
    document.getElementById('detailRequestMethod').textContent = log.requestMethod || '-';
    document.getElementById('detailIpAddress').textContent = log.ipAddress || '-';
    document.getElementById('detailExecutionTime').textContent = log.executionTime || '-';
    document.getElementById('detailUserAgent').textContent = log.userAgent || '-';
    
    // 处理可选字段
    const requestParamsSection = document.getElementById('requestParamsSection');
    const responseResultSection = document.getElementById('responseResultSection');
    const errorMessageSection = document.getElementById('errorMessageSection');
    
    if (log.requestParams) {
        requestParamsSection.style.display = 'block';
        document.getElementById('detailRequestParams').textContent = log.requestParams;
    } else {
        requestParamsSection.style.display = 'none';
    }
    
    if (log.responseResult) {
        responseResultSection.style.display = 'block';
        document.getElementById('detailResponseResult').textContent = log.responseResult;
    } else {
        responseResultSection.style.display = 'none';
    }
    
    if (log.errorMessage) {
        errorMessageSection.style.display = 'block';
        document.getElementById('detailErrorMessage').textContent = log.errorMessage;
    } else {
        errorMessageSection.style.display = 'none';
    }
    
    const modal = new bootstrap.Modal(document.getElementById('logDetailModal'));
    modal.show();
}

/**
 * 设置快速时间范围
 */
function setQuickTimeRange(range) {
    const now = new Date();
    let startTime;
    
    switch (range) {
        case 'today':
            startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            break;
        case 'week':
            startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
        case 'month':
            startTime = new Date(now.getFullYear(), now.getMonth(), 1);
            break;
        default:
            return;
    }
    
    document.getElementById('searchStartTime').value = formatDateTimeForInput(startTime);
    document.getElementById('searchEndTime').value = formatDateTimeForInput(now);
    
    // 自动搜索
    currentPage = 1;
    loadLogList();
}

/**
 * 重置搜索
 */
function resetSearch() {
    document.getElementById('searchForm').reset();
    setDefaultTimeRange();
    currentPage = 1;
    loadLogList();
}

/**
 * 导出日志
 */
function exportLogs() {
    const searchParams = {
        operationType: document.getElementById('searchOperationType').value,
        module: document.getElementById('searchModule').value,
        startTime: document.getElementById('searchStartTime').value,
        endTime: document.getElementById('searchEndTime').value
    };

    // 移除空值参数
    Object.keys(searchParams).forEach(key => {
        if (!searchParams[key]) {
            delete searchParams[key];
        }
    });

    fetch('/wkg/api/system/operation-log/export?' + new URLSearchParams(searchParams), {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('日志导出成功，共 ' + data.data.length + ' 条记录', 'success');
            // 这里可以实现实际的文件下载逻辑
        } else {
            showAlert('日志导出失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 显示清理日志模态框
 */
function showCleanModal() {
    const modal = new bootstrap.Modal(document.getElementById('cleanLogModal'));
    modal.show();
}

/**
 * 清理日志
 */
function cleanLogs() {
    const retentionDays = document.getElementById('retentionDays').value;
    
    if (!retentionDays || retentionDays < 1) {
        showAlert('请输入有效的保留天数', 'warning');
        return;
    }
    
    if (!confirm(`确定要删除 ${retentionDays} 天之前的所有日志吗？此操作不可恢复！`)) {
        return;
    }
    
    fetch(`/wkg/api/system/operation-log/clean?retentionDays=${retentionDays}`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`日志清理完成，共清理 ${data.data} 条记录`, 'success');
            bootstrap.Modal.getInstance(document.getElementById('cleanLogModal')).hide();
            loadLogList();
            loadStatistics();
        } else {
            showAlert('日志清理失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('网络错误，请稍后重试', 'danger');
    });
}

/**
 * 获取操作类型对应的徽章样式
 */
function getOperationTypeBadgeClass(operationType) {
    const typeMap = {
        'CREATE': 'bg-success',
        'UPDATE': 'bg-primary',
        'DELETE': 'bg-danger',
        'LOGIN': 'bg-info',
        'LOGOUT': 'bg-secondary',
        'QUERY': 'bg-light text-dark',
        'EXPORT': 'bg-warning',
        'IMPORT': 'bg-warning',
        'RESET_PASSWORD': 'bg-danger',
        'ASSIGN_ROLE': 'bg-primary',
        'GRANT_PERMISSION': 'bg-primary'
    };
    return typeMap[operationType] || 'bg-secondary';
}

/**
 * 获取操作类型文本
 */
function getOperationTypeText(operationType) {
    const typeMap = {
        'CREATE': '新增',
        'UPDATE': '修改',
        'DELETE': '删除',
        'LOGIN': '登录',
        'LOGOUT': '登出',
        'QUERY': '查询',
        'EXPORT': '导出',
        'IMPORT': '导入',
        'RESET_PASSWORD': '重置密码',
        'ASSIGN_ROLE': '分配角色',
        'GRANT_PERMISSION': '授权'
    };
    return typeMap[operationType] || operationType;
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}

/**
 * 格式化日期时间为输入框格式
 */
function formatDateTimeForInput(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

// Toast提示功能已移至通用库中
