/**
 * RBAC系统通用JavaScript工具库
 * 提供统一的用户体验和错误处理
 */

/**
 * 统一的Toast提示功能
 */
class ToastManager {
    constructor() {
        this.container = this.createContainer();
    }

    createContainer() {
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '1055';
            document.body.appendChild(container);
        }
        return container;
    }

    show(message, type = 'info', duration = 3000) {
        const toastId = 'toast-' + Date.now();
        const bgClass = this.getBackgroundClass(type);
        
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="${this.getIcon(type)} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;
        
        this.container.insertAdjacentHTML('beforeend', toastHtml);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: duration
        });
        
        toast.show();
        
        // 清理已隐藏的toast
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    }

    getBackgroundClass(type) {
        const classMap = {
            'success': 'bg-success',
            'danger': 'bg-danger',
            'warning': 'bg-warning',
            'info': 'bg-info',
            'primary': 'bg-primary'
        };
        return classMap[type] || 'bg-primary';
    }

    getIcon(type) {
        const iconMap = {
            'success': 'bi bi-check-circle',
            'danger': 'bi bi-exclamation-triangle',
            'warning': 'bi bi-exclamation-triangle',
            'info': 'bi bi-info-circle',
            'primary': 'bi bi-info-circle'
        };
        return iconMap[type] || 'bi bi-info-circle';
    }

    success(message, duration = 3000) {
        this.show(message, 'success', duration);
    }

    error(message, duration = 5000) {
        this.show(message, 'danger', duration);
    }

    warning(message, duration = 4000) {
        this.show(message, 'warning', duration);
    }

    info(message, duration = 3000) {
        this.show(message, 'info', duration);
    }
}

// 全局Toast管理器实例
const toastManager = new ToastManager();

/**
 * 统一的API请求处理
 */
class ApiClient {
    constructor() {
        this.baseUrl = '/wkg/api';
    }

    async request(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            }
        };

        const finalOptions = { ...defaultOptions, ...options };
        const fullUrl = url.startsWith('http') ? url : `${this.baseUrl}${url}`;

        try {
            const response = await fetch(fullUrl, finalOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || '操作失败');
            }

            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        return this.request(fullUrl);
    }

    async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    async delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }
}

// 全局API客户端实例
const apiClient = new ApiClient();

/**
 * 统一的确认对话框
 */
function confirmAction(message, title = '确认操作') {
    return new Promise((resolve) => {
        const confirmed = confirm(`${title}\n\n${message}`);
        resolve(confirmed);
    });
}

/**
 * 统一的加载状态管理
 */
class LoadingManager {
    constructor() {
        this.loadingElements = new Set();
    }

    show(element, text = '加载中...') {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        
        if (!element) return;

        element.disabled = true;
        const originalText = element.textContent;
        element.setAttribute('data-original-text', originalText);
        element.innerHTML = `<span class="spinner-border spinner-border-sm me-2" role="status"></span>${text}`;
        
        this.loadingElements.add(element);
    }

    hide(element) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }
        
        if (!element) return;

        element.disabled = false;
        const originalText = element.getAttribute('data-original-text');
        if (originalText) {
            element.textContent = originalText;
            element.removeAttribute('data-original-text');
        }
        
        this.loadingElements.delete(element);
    }

    hideAll() {
        this.loadingElements.forEach(element => {
            this.hide(element);
        });
    }
}

// 全局加载管理器实例
const loadingManager = new LoadingManager();

/**
 * 统一的表格分页渲染
 */
function renderPagination(pageData, containerId = 'pagination', onPageChange = null) {
    const pagination = document.getElementById(containerId);
    if (!pagination) return;

    pagination.innerHTML = '';

    const totalPages = pageData.pages || 1;
    const current = pageData.current || 1;

    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${current === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" data-page="${current - 1}">上一页</a>`;
    pagination.appendChild(prevLi);

    // 页码
    const startPage = Math.max(1, current - 2);
    const endPage = Math.min(totalPages, current + 2);

    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === current ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
        pagination.appendChild(li);
    }

    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${current === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" data-page="${current + 1}">下一页</a>`;
    pagination.appendChild(nextLi);

    // 绑定点击事件
    if (onPageChange) {
        pagination.addEventListener('click', function(e) {
            e.preventDefault();
            if (e.target.tagName === 'A' && !e.target.closest('.disabled')) {
                const page = parseInt(e.target.getAttribute('data-page'));
                if (page > 0 && page <= totalPages) {
                    onPageChange(page);
                }
            }
        });
    }
}

/**
 * 统一的日期时间格式化
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

/**
 * 统一的状态徽章渲染
 */
function renderStatusBadge(status, statusMap = null) {
    const defaultStatusMap = {
        1: { text: '启用', class: 'bg-success' },
        0: { text: '禁用', class: 'bg-danger' }
    };
    
    const map = statusMap || defaultStatusMap;
    const statusInfo = map[status] || { text: '未知', class: 'bg-secondary' };
    
    return `<span class="badge ${statusInfo.class}">${statusInfo.text}</span>`;
}

/**
 * 统一的表单验证
 */
function validateForm(formId, rules = {}) {
    const form = document.getElementById(formId);
    if (!form) return false;

    let isValid = true;
    const errors = [];

    Object.keys(rules).forEach(fieldName => {
        const field = form.querySelector(`[name="${fieldName}"]`);
        if (!field) return;

        const rule = rules[fieldName];
        const value = field.value.trim();

        // 必填验证
        if (rule.required && !value) {
            isValid = false;
            errors.push(`${rule.label || fieldName}不能为空`);
            field.classList.add('is-invalid');
        } else {
            field.classList.remove('is-invalid');
        }

        // 长度验证
        if (value && rule.minLength && value.length < rule.minLength) {
            isValid = false;
            errors.push(`${rule.label || fieldName}长度不能少于${rule.minLength}个字符`);
            field.classList.add('is-invalid');
        }

        if (value && rule.maxLength && value.length > rule.maxLength) {
            isValid = false;
            errors.push(`${rule.label || fieldName}长度不能超过${rule.maxLength}个字符`);
            field.classList.add('is-invalid');
        }

        // 正则验证
        if (value && rule.pattern && !rule.pattern.test(value)) {
            isValid = false;
            errors.push(rule.message || `${rule.label || fieldName}格式不正确`);
            field.classList.add('is-invalid');
        }
    });

    if (!isValid) {
        toastManager.error(errors[0]); // 只显示第一个错误
    }

    return isValid;
}

// 导出全局函数以保持向后兼容
window.showAlert = function(message, type = 'info') {
    toastManager.show(message, type);
};

window.showSuccess = function(message) {
    toastManager.success(message);
};

window.showError = function(message) {
    toastManager.error(message);
};

window.showWarning = function(message) {
    toastManager.warning(message);
};

window.showInfo = function(message) {
    toastManager.info(message);
};
