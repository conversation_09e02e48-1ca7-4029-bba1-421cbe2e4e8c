/**
 * RBAC权限管理系统专用样式
 * 与EdgeMind整体设计风格保持一致
 */

/* ==================== 基础样式 ==================== */
.rbac-container {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px;
}

.rbac-page-header {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
}

.rbac-page-title {
    color: #2c3e50;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.rbac-page-title i {
    color: #3498db;
    font-size: 1.2em;
}

/* ==================== 搜索表单样式 ==================== */
.rbac-search-form {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
}

.rbac-search-form .form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

.rbac-search-form .form-control,
.rbac-search-form .form-select {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px 12px;
    transition: all 0.2s ease;
}

.rbac-search-form .form-control:focus,
.rbac-search-form .form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.15);
}

/* ==================== 操作按钮组样式 ==================== */
.rbac-actions {
    background: white;
    border-radius: 12px;
    padding: 20px 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.rbac-btn {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.rbac-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.rbac-btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.rbac-btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.rbac-btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.rbac-btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.rbac-btn-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

/* ==================== 表格样式 ==================== */
.rbac-table-container {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.rbac-table {
    margin: 0;
    border: none;
}

.rbac-table thead th {
    background: #f8f9fa;
    border: none;
    color: #495057;
    font-weight: 600;
    padding: 16px 12px;
    border-bottom: 2px solid #e9ecef;
}

.rbac-table tbody td {
    padding: 16px 12px;
    border: none;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
}

.rbac-table tbody tr:hover {
    background: #f8f9fa;
    transition: background-color 0.2s ease;
}

.rbac-table tbody tr:last-child td {
    border-bottom: none;
}

/* ==================== 状态徽章样式 ==================== */
.rbac-status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.rbac-status-active {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.rbac-status-inactive {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* ==================== 操作按钮样式 ==================== */
.rbac-action-btn {
    padding: 6px 10px;
    border-radius: 6px;
    border: none;
    margin: 0 2px;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.rbac-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.rbac-action-edit {
    background: #3498db;
    color: white;
}

.rbac-action-delete {
    background: #e74c3c;
    color: white;
}

.rbac-action-view {
    background: #17a2b8;
    color: white;
}

.rbac-action-lock {
    background: #f39c12;
    color: white;
}

/* ==================== 分页样式 ==================== */
.rbac-pagination {
    margin-top: 24px;
    justify-content: center;
}

.rbac-pagination .page-link {
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 10px 16px;
    margin: 0 2px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.rbac-pagination .page-link:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
    transform: translateY(-1px);
}

.rbac-pagination .page-item.active .page-link {
    background: #3498db;
    border-color: #3498db;
    color: white;
}

.rbac-pagination .page-item.disabled .page-link {
    color: #6c757d;
    background: #f8f9fa;
    border-color: #dee2e6;
}

/* ==================== 模态框样式 ==================== */
.rbac-modal .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

.rbac-modal .modal-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border-radius: 12px 12px 0 0;
    padding: 20px 24px;
    border: none;
}

.rbac-modal .modal-title {
    font-weight: 600;
    margin: 0;
}

.rbac-modal .btn-close {
    filter: brightness(0) invert(1);
}

.rbac-modal .modal-body {
    padding: 24px;
}

.rbac-modal .modal-footer {
    padding: 20px 24px;
    border: none;
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

/* ==================== 表单样式 ==================== */
.rbac-form .form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

.rbac-form .form-control,
.rbac-form .form-select,
.rbac-form .form-check-input {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
}

.rbac-form .form-control:focus,
.rbac-form .form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.15);
}

.rbac-form .form-text {
    color: #6c757d;
    font-size: 0.875rem;
}

.rbac-form .text-danger {
    color: #e74c3c !important;
}

/* ==================== 权限树样式 ==================== */
.rbac-permission-tree {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 16px;
    background: #f8f9fa;
}

.rbac-permission-node {
    margin: 8px 0;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.rbac-permission-node:hover {
    background: white;
}

.rbac-permission-node .form-check {
    margin: 0;
}

.rbac-permission-node.level-0 {
    font-weight: 600;
    color: #2c3e50;
}

.rbac-permission-node.level-1 {
    margin-left: 20px;
    color: #34495e;
}

.rbac-permission-node.level-2 {
    margin-left: 40px;
    color: #7f8c8d;
}

/* ==================== Toast提示样式 ==================== */
.rbac-toast {
    border-radius: 8px;
    border: none;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.rbac-toast .toast-body {
    padding: 16px;
    font-weight: 500;
}

/* ==================== 统计卡片样式 ==================== */
.rbac-stats-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
    text-align: center;
    transition: transform 0.2s ease;
}

.rbac-stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.rbac-stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #3498db;
    margin: 0;
}

.rbac-stats-label {
    color: #7f8c8d;
    font-weight: 500;
    margin-top: 8px;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
    .rbac-container {
        padding: 12px;
    }
    
    .rbac-page-header,
    .rbac-search-form,
    .rbac-actions,
    .rbac-table-container {
        padding: 16px;
        margin-bottom: 16px;
    }
    
    .rbac-actions {
        flex-direction: column;
    }
    
    .rbac-btn {
        width: 100%;
        justify-content: center;
    }
    
    .rbac-table-container {
        overflow-x: auto;
    }
}

/* ==================== 加载动画 ==================== */
.rbac-loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: rbac-spin 1s linear infinite;
}

@keyframes rbac-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==================== 空状态样式 ==================== */
.rbac-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.rbac-empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #bdc3c7;
}

.rbac-empty-state h5 {
    color: #95a5a6;
    margin-bottom: 12px;
}

.rbac-empty-state p {
    color: #bdc3c7;
    margin: 0;
}
