/**
 * EdgeMind 全局CSS变量定义
 * 统一管理所有颜色、尺寸、动画等设计令牌
 * 
 * <AUTHOR>
 * @version 2.0
 */

:root {
    /* ========== 颜色系统 ========== */
    
    /* 主色调 - 蓝色系 */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    
    /* 语义化颜色 */
    --color-primary: var(--primary-600);
    --color-primary-light: var(--primary-100);
    --color-primary-dark: var(--primary-700);
    --color-primary-hover: var(--primary-700);
    --color-primary-active: var(--primary-800);
    
    /* 成功色 - 绿色系 */
    --success-50: #f0fdf4;
    --success-100: #dcfce7;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;
    
    /* 警告色 - 黄色系 */
    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    --warning-700: #b45309;
    
    /* 错误色 - 红色系 */
    --error-50: #fef2f2;
    --error-100: #fee2e2;
    --error-500: #ef4444;
    --error-600: #dc2626;
    --error-700: #b91c1c;
    
    /* 中性色 - 灰色系 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* 文本颜色 */
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-tertiary: var(--gray-500);
    --text-disabled: var(--gray-400);
    --text-inverse: #ffffff;
    
    /* 背景颜色 */
    --bg-primary: #ffffff;
    --bg-secondary: var(--gray-50);
    --bg-tertiary: var(--gray-100);
    --bg-hover: var(--gray-50);
    --bg-active: var(--gray-100);
    --bg-disabled: var(--gray-100);
    
    /* 边框颜色 */
    --border-primary: var(--gray-200);
    --border-secondary: var(--gray-300);
    --border-focus: var(--primary-500);
    --border-error: var(--error-500);
    --border-success: var(--success-500);
    
    /* ========== 尺寸系统 ========== */
    
    /* 间距 */
    --spacing-xs: 0.25rem;    /* 4px */
    --spacing-sm: 0.5rem;     /* 8px */
    --spacing-md: 0.75rem;    /* 12px */
    --spacing-lg: 1rem;       /* 16px */
    --spacing-xl: 1.5rem;     /* 24px */
    --spacing-2xl: 2rem;      /* 32px */
    --spacing-3xl: 3rem;      /* 48px */
    
    /* 圆角 */
    --radius-sm: 0.25rem;     /* 4px */
    --radius-md: 0.375rem;    /* 6px */
    --radius-lg: 0.5rem;      /* 8px */
    --radius-xl: 0.75rem;     /* 12px */
    --radius-2xl: 1rem;       /* 16px */
    --radius-full: 9999px;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* ========== 布局系统 ========== */
    
    /* 侧边栏 */
    --sidebar-width-expanded: 260px;
    --sidebar-width-collapsed: 68px;
    --sidebar-bg: var(--bg-primary);
    --sidebar-border: var(--border-primary);
    
    /* 头部导航 */
    --header-height: 64px;
    --header-bg: var(--bg-primary);
    --header-border: var(--border-primary);
    
    /* 内容区域 */
    --content-max-width: 1200px;
    --content-padding: var(--spacing-xl);
    
    /* ========== 动画系统 ========== */
    
    /* 过渡时间 */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    
    /* 过渡函数 */
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    
    /* 通用过渡 */
    --transition-all: all var(--transition-normal) var(--ease-in-out);
    --transition-colors: color var(--transition-fast) var(--ease-in-out), 
                        background-color var(--transition-fast) var(--ease-in-out), 
                        border-color var(--transition-fast) var(--ease-in-out);
    --transition-transform: transform var(--transition-normal) var(--ease-in-out);
    --transition-opacity: opacity var(--transition-fast) var(--ease-in-out);
    
    /* 侧边栏动画 */
    --sidebar-transition: width var(--transition-normal) var(--ease-in-out),
                         margin-left var(--transition-normal) var(--ease-in-out);
    
    /* ========== 字体系统 ========== */
    
    /* 字体族 */
    --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-mono: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
    
    /* 字体大小 */
    --text-xs: 0.75rem;       /* 12px */
    --text-sm: 0.875rem;      /* 14px */
    --text-base: 1rem;        /* 16px */
    --text-lg: 1.125rem;      /* 18px */
    --text-xl: 1.25rem;       /* 20px */
    --text-2xl: 1.5rem;       /* 24px */
    --text-3xl: 1.875rem;     /* 30px */
    --text-4xl: 2.25rem;      /* 36px */
    
    /* 行高 */
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    
    /* 字重 */
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    
    /* ========== 组件特定变量 ========== */
    
    /* 按钮 */
    --btn-height-sm: 32px;
    --btn-height-md: 40px;
    --btn-height-lg: 48px;
    --btn-padding-x-sm: var(--spacing-md);
    --btn-padding-x-md: var(--spacing-lg);
    --btn-padding-x-lg: var(--spacing-xl);
    
    /* 表单控件 */
    --input-height: 40px;
    --input-padding-x: var(--spacing-md);
    --input-border-width: 1px;
    --input-focus-ring: 0 0 0 3px rgba(59, 130, 246, 0.1);
    
    /* 表格 */
    --table-cell-padding-y: var(--spacing-md);
    --table-cell-padding-x: var(--spacing-lg);
    --table-header-bg: var(--gray-50);
    --table-hover-bg: var(--gray-50);
    
    /* 模态框 */
    --modal-backdrop: rgba(0, 0, 0, 0.5);
    --modal-border-radius: var(--radius-xl);
    --modal-shadow: var(--shadow-xl);
    
    /* 卡片 */
    --card-bg: var(--bg-primary);
    --card-border: var(--border-primary);
    --card-border-radius: var(--radius-lg);
    --card-shadow: var(--shadow-sm);
    --card-padding: var(--spacing-xl);
    
    /* ========== 响应式断点 ========== */
    
    /* 断点值（仅用于文档，CSS中使用媒体查询） */
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --breakpoint-2xl: 1400px;
    
    /* ========== Z-index 层级 ========== */
    
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
    --z-maximum: 2147483647;
}

/* ========== 暗色主题支持 ========== */
@media (prefers-color-scheme: dark) {
    :root {
        /* 暗色主题下的颜色覆盖 */
        --text-primary: var(--gray-100);
        --text-secondary: var(--gray-300);
        --text-tertiary: var(--gray-400);
        
        --bg-primary: var(--gray-900);
        --bg-secondary: var(--gray-800);
        --bg-tertiary: var(--gray-700);
        --bg-hover: var(--gray-800);
        --bg-active: var(--gray-700);
        
        --border-primary: var(--gray-700);
        --border-secondary: var(--gray-600);
        
        --sidebar-bg: var(--gray-900);
        --header-bg: var(--gray-900);
        --card-bg: var(--gray-800);
        --table-header-bg: var(--gray-800);
        --table-hover-bg: var(--gray-800);
    }
}

/* ========== 打印样式 ========== */
@media print {
    :root {
        --shadow-sm: none;
        --shadow-md: none;
        --shadow-lg: none;
        --shadow-xl: none;
    }
}
