/**
 * EdgeMind 统一组件样式库
 * 提供可复用的组件样式，避免重复定义
 * 
 * <AUTHOR>
 * @version 2.0
 */

/* ========== 按钮组件 ========== */

.btn-edgemind {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: 0 var(--btn-padding-x-md);
    height: var(--btn-height-md);
    border: var(--input-border-width) solid transparent;
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    line-height: 1;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-colors);
    user-select: none;
    white-space: nowrap;
}

.btn-edgemind:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 按钮尺寸变体 */
.btn-edgemind.btn-sm {
    height: var(--btn-height-sm);
    padding: 0 var(--btn-padding-x-sm);
    font-size: var(--text-xs);
}

.btn-edgemind.btn-lg {
    height: var(--btn-height-lg);
    padding: 0 var(--btn-padding-x-lg);
    font-size: var(--text-base);
}

/* 按钮颜色变体 */
.btn-edgemind.btn-primary {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    color: var(--text-inverse);
}

.btn-edgemind.btn-primary:hover:not(:disabled) {
    background-color: var(--color-primary-hover);
    border-color: var(--color-primary-hover);
}

.btn-edgemind.btn-secondary {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

.btn-edgemind.btn-secondary:hover:not(:disabled) {
    background-color: var(--bg-active);
}

.btn-edgemind.btn-outline {
    background-color: transparent;
    border-color: var(--color-primary);
    color: var(--color-primary);
}

.btn-edgemind.btn-outline:hover:not(:disabled) {
    background-color: var(--color-primary);
    color: var(--text-inverse);
}

.btn-edgemind.btn-ghost {
    background-color: transparent;
    border-color: transparent;
    color: var(--text-secondary);
}

.btn-edgemind.btn-ghost:hover:not(:disabled) {
    background-color: var(--bg-hover);
    color: var(--text-primary);
}

.btn-edgemind.btn-danger {
    background-color: var(--error-500);
    border-color: var(--error-500);
    color: var(--text-inverse);
}

.btn-edgemind.btn-danger:hover:not(:disabled) {
    background-color: var(--error-600);
    border-color: var(--error-600);
}

/* ========== 表单组件 ========== */

.form-control-edgemind {
    display: block;
    width: 100%;
    height: var(--input-height);
    padding: 0 var(--input-padding-x);
    border: var(--input-border-width) solid var(--border-primary);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--text-sm);
    line-height: var(--leading-normal);
    transition: var(--transition-colors);
}

.form-control-edgemind:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: var(--input-focus-ring);
}

.form-control-edgemind:disabled {
    background-color: var(--bg-disabled);
    color: var(--text-disabled);
    cursor: not-allowed;
}

.form-control-edgemind.is-invalid {
    border-color: var(--border-error);
}

.form-control-edgemind.is-valid {
    border-color: var(--border-success);
}

/* 表单标签 */
.form-label-edgemind {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
}

/* 表单组 */
.form-group-edgemind {
    margin-bottom: var(--spacing-lg);
}

/* 表单反馈 */
.form-feedback {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: var(--text-xs);
    line-height: var(--leading-tight);
}

.form-feedback.invalid-feedback {
    color: var(--error-500);
}

.form-feedback.valid-feedback {
    color: var(--success-500);
}

/* ========== 卡片组件 ========== */

.card-edgemind {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: var(--card-border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    transition: var(--transition-all);
}

.card-edgemind:hover {
    box-shadow: var(--shadow-md);
}

.card-edgemind-header {
    padding: var(--spacing-lg) var(--card-padding);
    border-bottom: 1px solid var(--border-primary);
    background-color: var(--bg-secondary);
}

.card-edgemind-body {
    padding: var(--card-padding);
}

.card-edgemind-footer {
    padding: var(--spacing-lg) var(--card-padding);
    border-top: 1px solid var(--border-primary);
    background-color: var(--bg-secondary);
}

.card-edgemind-title {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
}

/* ========== 表格组件 ========== */

.table-edgemind {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-primary);
}

.table-edgemind th,
.table-edgemind td {
    padding: var(--table-cell-padding-y) var(--table-cell-padding-x);
    text-align: left;
    border-bottom: 1px solid var(--border-primary);
    vertical-align: middle;
}

.table-edgemind th {
    background-color: var(--table-header-bg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    font-size: var(--text-sm);
}

.table-edgemind tbody tr:hover {
    background-color: var(--table-hover-bg);
}

.table-edgemind.table-striped tbody tr:nth-child(even) {
    background-color: var(--bg-secondary);
}

.table-edgemind.table-bordered {
    border: 1px solid var(--border-primary);
}

.table-edgemind.table-bordered th,
.table-edgemind.table-bordered td {
    border: 1px solid var(--border-primary);
}

/* ========== 模态框组件 ========== */

.modal-edgemind {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: var(--z-modal);
    display: none;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
}

.modal-edgemind.show {
    display: flex;
}

.modal-edgemind-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--modal-backdrop);
    z-index: -1;
}

.modal-edgemind-content {
    position: relative;
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    background-color: var(--bg-primary);
    border-radius: var(--modal-border-radius);
    box-shadow: var(--modal-shadow);
    overflow: hidden;
    animation: modalSlideIn var(--transition-normal) var(--ease-out);
}

.modal-edgemind-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-primary);
}

.modal-edgemind-title {
    margin: 0;
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
}

.modal-edgemind-close {
    background: none;
    border: none;
    font-size: var(--text-xl);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: var(--transition-colors);
}

.modal-edgemind-close:hover {
    background-color: var(--bg-hover);
    color: var(--text-primary);
}

.modal-edgemind-body {
    padding: var(--spacing-xl);
}

.modal-edgemind-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    border-top: 1px solid var(--border-primary);
}

/* ========== 加载状态组件 ========== */

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-primary);
    border-top-color: var(--color-primary);
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-fixed);
}

/* ========== 工具类 ========== */

.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-tertiary { color: var(--text-tertiary) !important; }
.text-success { color: var(--success-600) !important; }
.text-warning { color: var(--warning-600) !important; }
.text-error { color: var(--error-600) !important; }

.bg-primary { background-color: var(--bg-primary) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-tertiary { background-color: var(--bg-tertiary) !important; }

.border-primary { border-color: var(--border-primary) !important; }
.border-secondary { border-color: var(--border-secondary) !important; }

.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }

.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

/* ========== 动画定义 ========== */

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
