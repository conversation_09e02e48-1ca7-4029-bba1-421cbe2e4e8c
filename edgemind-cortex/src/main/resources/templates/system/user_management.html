<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 端智AI助手</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .search-form {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .btn-group-actions {
            margin-bottom: 15px;
        }
        .status-badge {
            font-size: 0.75rem;
        }
        .avatar-placeholder {
            width: 32px;
            height: 32px;
            background: #e9ecef;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="bi bi-person-fill me-2"></i>用户管理</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="#">系统管理</a></li>
                    <li class="breadcrumb-item active">用户管理</li>
                </ol>
            </nav>
        </div>

        <!-- 搜索表单 -->
        <div class="search-form">
            <form id="searchForm" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">用户名</label>
                    <input type="text" class="form-control" id="searchUsername" placeholder="请输入用户名">
                </div>
                <div class="col-md-3">
                    <label class="form-label">昵称</label>
                    <input type="text" class="form-control" id="searchNickname" placeholder="请输入昵称">
                </div>
                <div class="col-md-3">
                    <label class="form-label">部门</label>
                    <select class="form-select" id="searchDept">
                        <option value="">全部部门</option>
                        <!-- 部门选项将通过JS动态加载 -->
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">状态</label>
                    <select class="form-select" id="searchStatus">
                        <option value="">全部状态</option>
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search me-1"></i>搜索
                    </button>
                    <button type="button" class="btn btn-secondary ms-2" onclick="resetSearch()">
                        <i class="bi bi-arrow-clockwise me-1"></i>重置
                    </button>
                </div>
            </form>
        </div>

        <!-- 操作按钮组 -->
        <div class="btn-group-actions">
            <button type="button" class="btn btn-success" onclick="showCreateModal()">
                <i class="bi bi-plus-lg me-1"></i>新增用户
            </button>
            <button type="button" class="btn btn-danger" onclick="batchDelete()" disabled id="batchDeleteBtn">
                <i class="bi bi-trash me-1"></i>批量删除
            </button>
            <button type="button" class="btn btn-info" onclick="exportUsers()">
                <i class="bi bi-download me-1"></i>导出数据
            </button>
        </div>

        <!-- 用户列表表格 -->
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>
                                <input type="checkbox" class="form-check-input" id="selectAll">
                            </th>
                            <th>头像</th>
                            <th>用户名</th>
                            <th>昵称</th>
                            <th>邮箱</th>
                            <th>手机号</th>
                            <th>部门</th>
                            <th>状态</th>
                            <th>最后登录</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="userTableBody">
                        <!-- 用户数据将通过JS动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <nav aria-label="用户列表分页">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- 分页按钮将通过JS动态生成 -->
                </ul>
            </nav>
        </div>
    </div>

    <!-- 创建/编辑用户模态框 -->
    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userModalTitle">新增用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="userId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">用户名 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="username" required>
                                    <div class="form-text">用户名只能包含字母、数字和下划线</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">昵称</label>
                                    <input type="text" class="form-control" id="nickname">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">密码 <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="password" required>
                                    <div class="form-text">密码长度6-20位</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">确认密码 <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="confirmPassword" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">邮箱</label>
                                    <input type="email" class="form-control" id="email">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">手机号</label>
                                    <input type="tel" class="form-control" id="phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">所属部门</label>
                                    <select class="form-select" id="deptId">
                                        <option value="">请选择部门</option>
                                        <!-- 部门选项将通过JS动态加载 -->
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">状态</label>
                                    <select class="form-select" id="status">
                                        <option value="1">启用</option>
                                        <option value="0">禁用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">角色分配</label>
                                    <div id="roleCheckboxes" class="row">
                                        <!-- 角色复选框将通过JS动态生成 -->
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">备注</label>
                                    <textarea class="form-control" id="remark" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 重置密码模态框 -->
    <div class="modal fade" id="resetPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">重置密码</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="resetPasswordForm">
                        <input type="hidden" id="resetUserId">
                        <div class="mb-3">
                            <label class="form-label">新密码 <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="newPassword" required>
                            <div class="form-text">密码长度6-20位</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">确认新密码 <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="confirmNewPassword" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="resetPassword()">确认重置</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/wkg/js/shared/rbac-common.js"></script>
    <script src="/wkg/js/system/user-management.js"></script>
</body>
</html>
