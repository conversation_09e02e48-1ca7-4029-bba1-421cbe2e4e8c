<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志 - 端智AI助手</title>
    <!-- 使用EdgeMind本地资源 -->
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css">
    <!-- EdgeMind设计系统样式 -->
    <link rel="stylesheet" href="/wkg/css/base/main.css">
    <link rel="stylesheet" href="/wkg/css/components/modal-unified.css">
</head>
<body style="background-color: #f8f9fa; padding: 20px;">
    <div class="container-fluid">
        <!-- 页面标题 - 使用EdgeMind标准样式 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="text-primary fw-semibold mb-0">
                <i class="bi bi-journal-text me-2"></i>
                操作日志
            </h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item">
                        <a href="#" class="text-decoration-none" style="color: var(--text-secondary);">系统管理</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">操作日志</li>
                </ol>
            </nav>
        </div>

        <!-- 统计卡片 - 使用EdgeMind卡片样式 -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));">
                    <div class="card-body text-white text-center">
                        <div class="fs-2 fw-bold" id="totalOperations">-</div>
                        <div class="small opacity-75">总操作数</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #28a745, #20c997);">
                    <div class="card-body text-white text-center">
                        <div class="fs-2 fw-bold" id="todayOperations">-</div>
                        <div class="small opacity-75">今日操作</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #17a2b8, #6f42c1);">
                    <div class="card-body text-white text-center">
                        <div class="fs-2 fw-bold" id="successRate">-</div>
                        <div class="small opacity-75">成功率</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #fd7e14, #e83e8c);">
                    <div class="card-body text-white text-center">
                        <div class="fs-2 fw-bold" id="activeUsers">-</div>
                        <div class="small opacity-75">活跃用户</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索表单 - 使用EdgeMind卡片样式 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body">
                <form id="searchForm" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label fw-medium" style="color: var(--text-primary);">用户名</label>
                        <input type="text" class="form-control border-1" id="searchUsername"
                               placeholder="请输入用户名" style="border-color: var(--border-color);">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-medium" style="color: var(--text-primary);">操作类型</label>
                        <select class="form-select border-1" id="searchOperationType" style="border-color: var(--border-color);">
                            <option value="">全部类型</option>
                            <option value="CREATE">新增</option>
                            <option value="UPDATE">修改</option>
                            <option value="DELETE">删除</option>
                            <option value="LOGIN">登录</option>
                            <option value="LOGOUT">登出</option>
                            <option value="QUERY">查询</option>
                            <option value="EXPORT">导出</option>
                            <option value="IMPORT">导入</option>
                            <option value="RESET_PASSWORD">重置密码</option>
                            <option value="ASSIGN_ROLE">分配角色</option>
                            <option value="GRANT_PERMISSION">授权</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-medium" style="color: var(--text-primary);">操作模块</label>
                        <input type="text" class="form-control border-1" id="searchModule"
                               placeholder="请输入模块名称" style="border-color: var(--border-color);">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-medium" style="color: var(--text-primary);">操作状态</label>
                        <select class="form-select border-1" id="searchStatus" style="border-color: var(--border-color);">
                            <option value="">全部状态</option>
                            <option value="1">成功</option>
                            <option value="0">失败</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-medium" style="color: var(--text-primary);">开始时间</label>
                        <input type="datetime-local" class="form-control border-1" id="searchStartTime"
                               style="border-color: var(--border-color);">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-medium" style="color: var(--text-primary);">结束时间</label>
                        <input type="datetime-local" class="form-control border-1" id="searchEndTime"
                               style="border-color: var(--border-color);">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary px-4"
                                style="background-color: var(--primary-color); border-color: var(--primary-color);">
                            <i class="bi bi-search me-2"></i>搜索
                        </button>
                        <button type="button" class="btn btn-outline-secondary ms-2 px-3" onclick="resetSearch()">
                            <i class="bi bi-arrow-clockwise me-2"></i>重置
                        </button>
                        <button type="button" class="btn btn-outline-info ms-2 px-3" onclick="setQuickTimeRange('today')">
                            <i class="bi bi-calendar-day me-2"></i>今天
                        </button>
                        <button type="button" class="btn btn-outline-info ms-2 px-3" onclick="setQuickTimeRange('week')">
                            <i class="bi bi-calendar-week me-2"></i>本周
                        </button>
                        <button type="button" class="btn btn-outline-info ms-2 px-3" onclick="setQuickTimeRange('month')">
                            <i class="bi bi-calendar-month me-2"></i>本月
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 操作按钮组 - 使用EdgeMind按钮样式 -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body">
                <div class="d-flex flex-wrap gap-2">
                    <button type="button" class="btn btn-success px-4" onclick="exportLogs()">
                        <i class="bi bi-download me-2"></i>导出日志
                    </button>
                    <button type="button" class="btn btn-warning px-4" onclick="showCleanModal()">
                        <i class="bi bi-trash3 me-2"></i>清理日志
                    </button>
                    <button type="button" class="btn btn-info px-4" onclick="refreshStatistics()">
                        <i class="bi bi-arrow-clockwise me-2"></i>刷新统计
                    </button>
                </div>
            </div>
        </div>

        <!-- 日志列表表格 - 使用EdgeMind表格样式 -->
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0">
                        <thead style="background-color: var(--primary-light);">
                            <tr>
                                <th class="fw-semibold" style="color: var(--text-primary);">操作时间</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">用户</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">操作类型</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">模块</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">操作描述</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">IP地址</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">执行时间</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">状态</th>
                                <th class="fw-semibold" style="color: var(--text-primary);">操作</th>
                            </tr>
                        </thead>
                        <tbody id="logTableBody">
                            <!-- 日志数据将通过JS动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 - 使用EdgeMind分页样式 -->
                <nav aria-label="日志列表分页" class="mt-4">
                    <ul class="pagination justify-content-center mb-0" id="pagination">
                        <!-- 分页按钮将通过JS动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 日志详情模态框 - 使用EdgeMind模态框样式 -->
    <div class="modal fade modal-unified" id="logDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content border-0 shadow">
                <div class="modal-header" style="background-color: var(--primary-light); border-bottom: 1px solid var(--border-color);">
                    <h5 class="modal-title fw-semibold" style="color: var(--text-primary);">
                        <i class="bi bi-info-circle me-2"></i>操作日志详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="fw-medium text-muted small">操作用户</label>
                            <div id="detailUsername" class="fw-medium"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="fw-medium text-muted small">操作时间</label>
                            <div id="detailCreateTime" class="fw-medium"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="fw-medium text-muted small">操作类型</label>
                            <div id="detailOperationType" class="fw-medium"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="fw-medium text-muted small">操作模块</label>
                            <div id="detailModule" class="fw-medium"></div>
                        </div>
                        <div class="col-12">
                            <label class="fw-medium text-muted small">操作描述</label>
                            <div id="detailDescription" class="fw-medium"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="fw-medium text-muted small">请求URL</label>
                            <div id="detailRequestUrl" class="fw-medium text-break"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="fw-medium text-muted small">请求方法</label>
                            <div id="detailRequestMethod" class="fw-medium"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="fw-medium text-muted small">IP地址</label>
                            <div id="detailIpAddress" class="fw-medium"></div>
                        </div>
                        <div class="col-md-6">
                            <label class="fw-medium text-muted small">执行时间</label>
                            <div id="detailExecutionTime" class="fw-medium"></div>
                        </div>
                        <div class="col-12">
                            <label class="fw-medium text-muted small">用户代理</label>
                            <div id="detailUserAgent" class="text-break small" style="color: var(--text-secondary);"></div>
                        </div>
                        <div class="col-12" id="requestParamsSection">
                            <label class="fw-medium text-muted small">请求参数</label>
                            <pre id="detailRequestParams" class="p-3 rounded mt-1 small"
                                 style="background-color: var(--hover-bg); border: 1px solid var(--border-color);"></pre>
                        </div>
                        <div class="col-12" id="responseResultSection">
                            <label class="fw-medium text-muted small">响应结果</label>
                            <pre id="detailResponseResult" class="p-3 rounded mt-1 small"
                                 style="background-color: var(--hover-bg); border: 1px solid var(--border-color);"></pre>
                        </div>
                        <div class="col-12" id="errorMessageSection">
                            <label class="fw-medium text-muted small">错误信息</label>
                            <div id="detailErrorMessage" class="text-danger fw-medium"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="background-color: #f8f9fa; border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 清理日志模态框 - 使用EdgeMind模态框样式 -->
    <div class="modal fade modal-unified" id="cleanLogModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content border-0 shadow">
                <div class="modal-header" style="background-color: #fff3cd; border-bottom: 1px solid #ffeaa7;">
                    <h5 class="modal-title fw-semibold text-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>清理操作日志
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning border-0" style="background-color: #fff3cd;">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        此操作将删除指定天数之前的所有操作日志，删除后无法恢复！
                    </div>
                    <div class="mb-3">
                        <label class="form-label fw-medium" style="color: var(--text-primary);">
                            保留天数 <span class="text-danger">*</span>
                        </label>
                        <input type="number" class="form-control border-1" id="retentionDays"
                               value="90" min="1" max="365" style="border-color: var(--border-color);">
                        <div class="form-text" style="color: var(--text-secondary);">
                            将删除 <span class="fw-medium">90</span> 天之前的日志记录
                        </div>
                    </div>
                </div>
                <div class="modal-footer" style="background-color: #f8f9fa; border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn btn-outline-secondary px-4" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger px-4" onclick="cleanLogs()">确认清理</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 使用EdgeMind本地JavaScript资源 -->
    <script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
    <script type="module" src="/wkg/js/shared/components/modal-component.js"></script>
    <script>
        // 导入EdgeMind Toast组件
        import { showToast } from '/wkg/js/shared/components/modal-component.js';
        // 将showToast函数暴露到全局作用域
        window.showToast = showToast;
    </script>
    <script src="/wkg/js/system/operation-log.js"></script>
</body>
</html>
