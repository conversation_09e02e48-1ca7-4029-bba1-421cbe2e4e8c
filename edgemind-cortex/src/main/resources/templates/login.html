<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 端智AI助手</title>
    <!-- 使用EdgeMind本地资源 -->
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="/wkg/css/vendor/bootstrap-icons.css">
    <!-- EdgeMind设计系统样式 -->
    <link rel="stylesheet" href="/wkg/css/base/main.css">
    <style>
        :root {
            --primary-color: #4285f4;
            --primary-light: #e8f0fe;
            --text-primary: #2c3e50;
            --text-secondary: #5f6368;
            --border-color: #e6f0ff;
            --hover-bg: #f5f8ff;
            --error-color: #dc3545;
            --success-color: #28a745;
        }
        
        body {
            background: linear-gradient(135deg, var(--primary-color) 0%, #667eea 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .login-header {
            background: linear-gradient(135deg, var(--primary-color), #667eea);
            color: white;
            padding: 40px 30px 30px;
            text-align: center;
        }
        
        .login-body {
            padding: 40px 30px;
        }
        
        .form-control {
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(66, 133, 244, 0.25);
        }
        
        .btn-login {
            background: var(--primary-color);
            border: none;
            border-radius: 8px;
            padding: 12px;
            font-size: 16px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            background: #3367d6;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
        }
        
        .btn-login:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .alert {
            border: none;
            border-radius: 8px;
            padding: 12px 16px;
            margin-bottom: 20px;
        }
        
        .alert-danger {
            background: #ffeaea;
            color: var(--error-color);
            border-left: 4px solid var(--error-color);
        }
        
        .alert-success {
            background: #eafaf1;
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }
        
        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .input-group-text {
            background: var(--hover-bg);
            border: 2px solid var(--border-color);
            border-right: none;
            color: var(--text-secondary);
        }
        
        .input-group .form-control {
            border-left: none;
        }
        
        .input-group:focus-within .input-group-text {
            border-color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- 登录头部 -->
            <div class="login-header">
                <div class="mb-3">
                    <i class="bi bi-shield-check" style="font-size: 3rem;"></i>
                </div>
                <h2 class="fw-bold mb-2">端智AI助手</h2>
                <p class="mb-0 opacity-90">权限管理系统</p>
            </div>
            
            <!-- 登录表单 -->
            <div class="login-body">
                <!-- 错误提示 -->
                <div id="errorAlert" class="alert alert-danger" style="display: none;">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <span id="errorMessage"></span>
                </div>
                
                <!-- 成功提示 -->
                <div id="successAlert" class="alert alert-success" style="display: none;">
                    <i class="bi bi-check-circle me-2"></i>
                    <span id="successMessage"></span>
                </div>
                
                <form id="loginForm" novalidate>
                    <!-- 用户名输入 -->
                    <div class="mb-3">
                        <label class="form-label fw-medium" style="color: var(--text-primary);">
                            用户名 <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-person"></i>
                            </span>
                            <input type="text" class="form-control" id="username" name="username" 
                                   placeholder="请输入用户名" required autocomplete="username">
                        </div>
                        <div class="invalid-feedback" id="usernameError"></div>
                    </div>
                    
                    <!-- 密码输入 -->
                    <div class="mb-3">
                        <label class="form-label fw-medium" style="color: var(--text-primary);">
                            密码 <span class="text-danger">*</span>
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password" 
                                   placeholder="请输入密码" required autocomplete="current-password">
                            <button type="button" class="btn btn-outline-secondary" id="togglePassword">
                                <i class="bi bi-eye" id="togglePasswordIcon"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback" id="passwordError"></div>
                    </div>
                    
                    <!-- 记住我选项 -->
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="rememberMe" name="rememberMe">
                            <label class="form-check-label" for="rememberMe" style="color: var(--text-secondary);">
                                记住我（7天内免登录）
                            </label>
                        </div>
                    </div>
                    
                    <!-- 登录按钮 -->
                    <button type="submit" class="btn btn-login" id="loginBtn">
                        <span class="btn-text">登录</span>
                        <div class="loading-spinner" id="loadingSpinner"></div>
                    </button>
                </form>
                
                <!-- 底部信息 -->
                <div class="text-center mt-4">
                    <small style="color: var(--text-secondary);">
                        © 2025 端智AI助手 - 企业级权限管理系统
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- 使用EdgeMind本地JavaScript资源 -->
    <script src="/wkg/js/vendor/bootstrap.bundle.min.js"></script>
    <script src="/wkg/js/login.js"></script>
</body>
</html>
