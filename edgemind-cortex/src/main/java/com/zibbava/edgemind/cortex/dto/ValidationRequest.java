package com.zibbava.edgemind.cortex.dto;

import lombok.Data;
import java.util.Map;

/**
 * 表单验证请求DTO
 * 用于前端向后端发送表单验证请求
 */
@Data
public class ValidationRequest {
    
    /**
     * 表单类型 - 用于区分不同的表单验证规则
     * 例如: "user", "role", "department", "permission"
     */
    private String formType;
    
    /**
     * 表单数据 - 包含所有需要验证的字段
     */
    private Map<String, Object> formData;
    
    /**
     * 验证模式 - 用于区分不同的验证场景
     * 例如: "create", "update", "partial"
     */
    private String validationMode;
    
    /**
     * 排除的字段 - 在更新时排除某些字段的验证
     */
    private String[] excludeFields;
    
    /**
     * 额外的验证参数
     */
    private Map<String, Object> extraParams;
}
