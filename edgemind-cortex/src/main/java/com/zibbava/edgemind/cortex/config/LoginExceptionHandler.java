package com.zibbava.edgemind.cortex.config;

import cn.dev33.satoken.exception.NotLoginException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * 登录异常处理器
 * 专门处理页面请求的登录重定向
 * 
 * <AUTHOR> Team
 * @since 2025-06-28
 */
@Slf4j
@ControllerAdvice(annotations = Controller.class)
@Order(1) // 优先级高于GlobalExceptionHandler
public class LoginExceptionHandler {

    /**
     * 处理页面请求的未登录异常
     * 重定向到登录页面
     */
    @ExceptionHandler(NotLoginException.class)
    public ModelAndView handleNotLoginException(NotLoginException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String queryString = request.getQueryString();
        
        // 构建原始请求URL
        String originalUrl = requestURI;
        if (queryString != null && !queryString.isEmpty()) {
            originalUrl += "?" + queryString;
        }
        
        log.info("用户未登录，重定向到登录页面。原始请求: {}", originalUrl);
        
        try {
            // URL编码重定向参数
            String encodedRedirect = URLEncoder.encode(originalUrl, "UTF-8");
            return new ModelAndView("redirect:/login?redirect=" + encodedRedirect);
        } catch (UnsupportedEncodingException ex) {
            log.error("URL编码失败", ex);
            return new ModelAndView("redirect:/login");
        }
    }
}
