package com.zibbava.edgemind.cortex.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@Slf4j
public class SaTokenConfigure implements WebMvcConfigurer {

    // 注册 Sa-Token 拦截器，打开注解式鉴权功能
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        log.info("配置Sa-Token拦截器...");
        // 注册 Sa-Token 拦截器，打开注解式鉴权功能
        registry.addInterceptor(new SaInterceptor(handle -> {
            // 登录校验 -- 拦截所有路径，并排除登录相关路径
            StpUtil.checkLogin();
            // 可以在此处进行更多的权限校验...
        }))
        .addPathPatterns("/**") // 拦截所有路径
        .excludePathPatterns(
            // 登录相关页面和API
            "/login", "/logout", "/api/auth/**",
            "/auth/login", "/auth/register", "/auth/check-login", // 排除原有登录、注册、检查登录状态接口
            // 静态资源
            "/", "/index.html",
            "/css/**", "/js/**", "/images/**", "/fonts/**",
            "/wkg/css/**", "/wkg/js/**", "/wkg/static/**", // EdgeMind静态资源
            // 系统页面
            "/error/**", // 排除错误页面
            "/license", "/license/content", // 排除授权页面
            "/api/license/**" // 排除授权相关API
        )
        // 以下是排除不需要二次认证的流式响应接口
        .excludePathPatterns(
            "/api/**/stream"
        );
    }
}