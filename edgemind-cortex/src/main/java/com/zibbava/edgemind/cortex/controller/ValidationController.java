package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.zibbava.edgemind.cortex.dto.ApiResponse;
import com.zibbava.edgemind.cortex.dto.ValidationRequest;
import com.zibbava.edgemind.cortex.dto.ValidationResponse;
import com.zibbava.edgemind.cortex.service.FormValidationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 表单验证控制器
 * 提供统一的表单验证API
 */
@Slf4j
@RestController
@RequestMapping("/api/validation")
@RequiredArgsConstructor
public class ValidationController {

    private final FormValidationService formValidationService;

    /**
     * 通用表单验证接口
     */
    @PostMapping("/validate-form")
    @SaCheckLogin
    public ResponseEntity<ApiResponse<ValidationResponse>> validateForm(@RequestBody ValidationRequest request) {
        try {
            log.info("表单验证请求 - 类型: {}, 模式: {}", request.getFormType(), request.getValidationMode());
            
            ValidationResponse result = formValidationService.validateForm(request);
            
            if (result.isSuccess()) {
                log.debug("表单验证通过 - 类型: {}", request.getFormType());
                return ResponseEntity.ok(ApiResponse.success("验证通过", result));
            } else {
                log.warn("表单验证失败 - 类型: {}, 错误: {}", request.getFormType(), result.getFieldErrors());
                return ResponseEntity.ok(ApiResponse.success("验证完成", result));
            }
        } catch (Exception e) {
            log.error("表单验证异常", e);
            ValidationResponse errorResult = ValidationResponse.error("验证过程中发生错误");
            return ResponseEntity.ok(ApiResponse.error(500, "验证失败"));
        }
    }

    /**
     * 用户表单验证
     */
    @PostMapping("/validate-user")
    @SaCheckLogin
    public ResponseEntity<ApiResponse<ValidationResponse>> validateUser(
            @RequestBody Map<String, Object> formData,
            @RequestParam(defaultValue = "create") String mode,
            @RequestParam(required = false) Long excludeUserId) {
        try {
            ValidationResponse result = formValidationService.validateUserForm(formData, mode, excludeUserId);
            return ResponseEntity.ok(ApiResponse.success("验证完成", result));
        } catch (Exception e) {
            log.error("用户表单验证异常", e);
            return ResponseEntity.ok(ApiResponse.error(500, "验证失败"));
        }
    }

    /**
     * 角色表单验证
     */
    @PostMapping("/validate-role")
    @SaCheckLogin
    public ResponseEntity<ApiResponse<ValidationResponse>> validateRole(
            @RequestBody Map<String, Object> formData,
            @RequestParam(defaultValue = "create") String mode,
            @RequestParam(required = false) Long excludeRoleId) {
        try {
            ValidationResponse result = formValidationService.validateRoleForm(formData, mode, excludeRoleId);
            return ResponseEntity.ok(ApiResponse.success("验证完成", result));
        } catch (Exception e) {
            log.error("角色表单验证异常", e);
            return ResponseEntity.ok(ApiResponse.error(500, "验证失败"));
        }
    }

    /**
     * 部门表单验证
     */
    @PostMapping("/validate-department")
    @SaCheckLogin
    public ResponseEntity<ApiResponse<ValidationResponse>> validateDepartment(
            @RequestBody Map<String, Object> formData,
            @RequestParam(defaultValue = "create") String mode,
            @RequestParam(required = false) Long excludeDeptId) {
        try {
            ValidationResponse result = formValidationService.validateDepartmentForm(formData, mode, excludeDeptId);
            return ResponseEntity.ok(ApiResponse.success("验证完成", result));
        } catch (Exception e) {
            log.error("部门表单验证异常", e);
            return ResponseEntity.ok(ApiResponse.error(500, "验证失败"));
        }
    }

    /**
     * 权限表单验证
     */
    @PostMapping("/validate-permission")
    @SaCheckLogin
    public ResponseEntity<ApiResponse<ValidationResponse>> validatePermission(
            @RequestBody Map<String, Object> formData,
            @RequestParam(defaultValue = "create") String mode,
            @RequestParam(required = false) Long excludePermissionId) {
        try {
            ValidationResponse result = formValidationService.validatePermissionForm(formData, mode, excludePermissionId);
            return ResponseEntity.ok(ApiResponse.success("验证完成", result));
        } catch (Exception e) {
            log.error("权限表单验证异常", e);
            return ResponseEntity.ok(ApiResponse.error(500, "验证失败"));
        }
    }

    /**
     * 字段唯一性检查
     */
    @PostMapping("/check-unique")
    @SaCheckLogin
    public ResponseEntity<ApiResponse<Boolean>> checkUnique(
            @RequestParam String tableName,
            @RequestParam String fieldName,
            @RequestParam String fieldValue,
            @RequestParam(required = false) Long excludeId) {
        try {
            boolean isUnique = formValidationService.isFieldUnique(tableName, fieldName, fieldValue, excludeId);
            return ResponseEntity.ok(ApiResponse.success("检查完成", isUnique));
        } catch (Exception e) {
            log.error("唯一性检查异常", e);
            return ResponseEntity.ok(ApiResponse.error(500, "检查失败"));
        }
    }

    /**
     * 批量字段验证
     */
    @PostMapping("/validate-fields")
    @SaCheckLogin
    public ResponseEntity<ApiResponse<Map<String, Boolean>>> validateFields(@RequestBody Map<String, Object> request) {
        try {
            // 这里可以实现批量字段验证逻辑
            // 暂时返回成功响应
            return ResponseEntity.ok(ApiResponse.success("批量验证完成", Map.of()));
        } catch (Exception e) {
            log.error("批量字段验证异常", e);
            return ResponseEntity.ok(ApiResponse.error(500, "验证失败"));
        }
    }
}
