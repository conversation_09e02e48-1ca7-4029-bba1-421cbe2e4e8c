package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zibbava.edgemind.cortex.annotation.OperationLog;
import com.zibbava.edgemind.cortex.dto.ApiResponse;
import com.zibbava.edgemind.cortex.dto.UserManagementDTO.*;
import com.zibbava.edgemind.cortex.entity.OperationLog.OperationType;
import com.zibbava.edgemind.cortex.service.UserManagementService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 用户管理控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/system/user")
@RequiredArgsConstructor
@Validated
public class UserManagementController {

    private final UserManagementService userManagementService;

    /**
     * 分页查询用户列表
     */
    @GetMapping("/list")
    @SaCheckPermission("user:manage:list")
    @OperationLog(operationType = OperationType.QUERY, module = "用户管理", description = "查询用户列表")
    public ResponseEntity<ApiResponse<IPage<UserResponse>>> getUserPage(@Valid UserQueryRequest request) {
        IPage<UserResponse> result = userManagementService.getUserPage(request);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 获取用户详情
     */
    @GetMapping("/{userId}")
    @SaCheckPermission("user:manage:list")
    @OperationLog(operationType = OperationType.QUERY, module = "用户管理", description = "查询用户详情")
    public ResponseEntity<ApiResponse<UserResponse>> getUserById(@PathVariable Long userId) {
        UserResponse result = userManagementService.getUserById(userId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", result));
    }

    /**
     * 创建用户
     */
    @PostMapping
    @SaCheckPermission("user:manage:create")
    @OperationLog(operationType = OperationType.CREATE, module = "用户管理", description = "创建用户")
    public ResponseEntity<ApiResponse<Long>> createUser(@Valid @RequestBody CreateUserRequest request) {
        Long userId = userManagementService.createUser(request);
        return ResponseEntity.ok(ApiResponse.success("用户创建成功", userId));
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/{userId}")
    @SaCheckPermission("user:manage:update")
    @OperationLog(operationType = OperationType.UPDATE, module = "用户管理", description = "更新用户信息")
    public ResponseEntity<ApiResponse<Void>> updateUser(@PathVariable Long userId, 
                                                       @Valid @RequestBody UpdateUserRequest request) {
        request.setId(userId);
        userManagementService.updateUser(request);
        return ResponseEntity.ok(ApiResponse.success("用户信息更新成功"));
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{userId}")
    @SaCheckPermission("user:manage:delete")
    @OperationLog(operationType = OperationType.DELETE, module = "用户管理", description = "删除用户")
    public ResponseEntity<ApiResponse<Void>> deleteUser(@PathVariable Long userId) {
        userManagementService.deleteUser(userId);
        return ResponseEntity.ok(ApiResponse.success("用户删除成功"));
    }

    /**
     * 批量删除用户
     */
    @DeleteMapping("/batch")
    @SaCheckPermission("user:manage:delete")
    @OperationLog(operationType = OperationType.DELETE, module = "用户管理", description = "批量删除用户")
    public ResponseEntity<ApiResponse<Void>> batchDeleteUsers(@RequestBody List<Long> userIds) {
        userManagementService.batchDeleteUsers(userIds);
        return ResponseEntity.ok(ApiResponse.success("用户批量删除成功"));
    }

    /**
     * 重置用户密码（管理员操作）
     */
    @PostMapping("/{userId}/reset-password")
    @SaCheckPermission("user:manage:reset-password")
    @OperationLog(operationType = OperationType.RESET_PASSWORD, module = "用户管理", description = "重置用户密码")
    public ResponseEntity<ApiResponse<Void>> resetPassword(@PathVariable Long userId,
                                                          @Valid @RequestBody ResetPasswordRequest request) {
        request.setUserId(userId);
        userManagementService.resetPassword(request);
        return ResponseEntity.ok(ApiResponse.success("密码重置成功"));
    }

    /**
     * 修改密码（用户自己操作）
     */
    @PostMapping("/{userId}/change-password")
    @SaCheckPermission("user:manage:change-password")
    @OperationLog(operationType = OperationType.UPDATE, module = "用户管理", description = "修改用户密码")
    public ResponseEntity<ApiResponse<Void>> changePassword(@PathVariable Long userId,
                                                           @Valid @RequestBody ChangePasswordRequest request) {
        request.setUserId(userId);
        userManagementService.changePassword(request);
        return ResponseEntity.ok(ApiResponse.success("密码修改成功"));
    }

    /**
     * 为用户分配角色
     */
    @PostMapping("/{userId}/roles")
    @SaCheckPermission("user:manage:assign-role")
    @OperationLog(operationType = OperationType.ASSIGN_ROLE, module = "用户管理", description = "分配用户角色")
    public ResponseEntity<ApiResponse<Void>> assignRoles(@PathVariable Long userId,
                                                        @Valid @RequestBody AssignRoleRequest request) {
        request.setUserId(userId);
        userManagementService.assignRoles(request);
        return ResponseEntity.ok(ApiResponse.success("角色分配成功"));
    }

    /**
     * 切换用户状态
     */
    @PostMapping("/{userId}/toggle-status")
    @SaCheckPermission("user:manage:update")
    @OperationLog(operationType = OperationType.UPDATE, module = "用户管理", description = "切换用户状态")
    public ResponseEntity<ApiResponse<Void>> toggleUserStatus(@PathVariable Long userId,
                                                             @Valid @RequestBody ToggleStatusRequest request) {
        request.setUserId(userId);
        userManagementService.toggleUserStatus(request);
        return ResponseEntity.ok(ApiResponse.success("用户状态更新成功"));
    }

    /**
     * 锁定用户
     */
    @PostMapping("/{userId}/lock")
    @SaCheckPermission("user:manage:update")
    @OperationLog(operationType = OperationType.UPDATE, module = "用户管理", description = "锁定用户账户")
    public ResponseEntity<ApiResponse<Void>> lockUser(@PathVariable Long userId,
                                                     @RequestParam(required = false) String reason) {
        userManagementService.lockUser(userId, reason);
        return ResponseEntity.ok(ApiResponse.success("用户账户已锁定"));
    }

    /**
     * 解锁用户
     */
    @PostMapping("/{userId}/unlock")
    @SaCheckPermission("user:manage:update")
    @OperationLog(operationType = OperationType.UPDATE, module = "用户管理", description = "解锁用户账户")
    public ResponseEntity<ApiResponse<Void>> unlockUser(@PathVariable Long userId) {
        userManagementService.unlockUser(userId);
        return ResponseEntity.ok(ApiResponse.success("用户账户已解锁"));
    }

    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check-username")
    @SaCheckPermission("user:manage:list")
    public ResponseEntity<ApiResponse<Boolean>> checkUsername(@RequestParam String username) {
        boolean exists = userManagementService.isUsernameExists(username);
        return ResponseEntity.ok(ApiResponse.success("检查完成", exists));
    }

    /**
     * 获取用户的角色列表
     */
    @GetMapping("/{userId}/roles")
    @SaCheckPermission("user:manage:list")
    @OperationLog(operationType = OperationType.QUERY, module = "用户管理", description = "查询用户角色")
    public ResponseEntity<ApiResponse<List<RoleResponse>>> getUserRoles(@PathVariable Long userId) {
        List<RoleResponse> roles = userManagementService.getUserRoles(userId);
        return ResponseEntity.ok(ApiResponse.success("查询成功", roles));
    }

    /**
     * 导出用户数据
     */
    @PostMapping("/export")
    @SaCheckPermission("user:manage:export")
    @OperationLog(operationType = OperationType.EXPORT, module = "用户管理", description = "导出用户数据")
    public ResponseEntity<ApiResponse<List<UserResponse>>> exportUsers(@Valid @RequestBody UserQueryRequest request) {
        List<UserResponse> users = userManagementService.exportUsers(request);
        return ResponseEntity.ok(ApiResponse.success("导出成功", users));
    }
}
