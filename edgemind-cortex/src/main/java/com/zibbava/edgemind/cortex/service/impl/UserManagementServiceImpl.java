package com.zibbava.edgemind.cortex.service.impl;

import cn.dev33.satoken.secure.SaSecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zibbava.edgemind.cortex.dto.UserManagementDTO.*;
import com.zibbava.edgemind.cortex.entity.*;
import com.zibbava.edgemind.cortex.exception.BadRequestException;
import com.zibbava.edgemind.cortex.exception.ResourceNotFoundException;
import com.zibbava.edgemind.cortex.mapper.UserMapper;
import com.zibbava.edgemind.cortex.mapper.UserRoleMapper;
import com.zibbava.edgemind.cortex.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户管理服务实现类
 * 
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserManagementServiceImpl implements UserManagementService {

    private final UserMapper userMapper;
    private final UserRoleMapper userRoleMapper;
    private final RoleService roleService;
    private final DepartmentService departmentService;
    private final OperationLogService operationLogService;

    @Override
    public IPage<UserResponse> getUserPage(UserQueryRequest request) {
        Page<User> page = new Page<>(request.getPageNum(), request.getPageSize());
        
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.hasText(request.getUsername()), User::getUsername, request.getUsername())
                   .like(StringUtils.hasText(request.getNickname()), User::getNickname, request.getNickname())
                   .like(StringUtils.hasText(request.getEmail()), User::getEmail, request.getEmail())
                   .like(StringUtils.hasText(request.getPhone()), User::getPhone, request.getPhone())
                   .eq(request.getDeptId() != null, User::getDeptId, request.getDeptId())
                   .eq(request.getStatus() != null, User::getStatus, request.getStatus())
                   .orderByDesc(User::getCreateTime);
        
        IPage<User> userPage = userMapper.selectPage(page, queryWrapper);
        
        // 转换为响应DTO
        IPage<UserResponse> responsePage = userPage.convert(this::convertToUserResponse);
        
        return responsePage;
    }

    @Override
    public UserResponse getUserById(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在: " + userId);
        }
        
        UserResponse response = convertToUserResponse(user);
        
        // 获取用户角色
        List<Role> roles = roleService.findRolesByUserId(userId);
        response.setRoles(roles.stream()
                .map(this::convertToRoleResponse)
                .collect(Collectors.toList()));
        
        return response;
    }

    @Override
    @Transactional
    public Long createUser(CreateUserRequest request) {
        // 验证用户名唯一性
        if (isUsernameExists(request.getUsername())) {
            throw new BadRequestException("用户名已存在: " + request.getUsername());
        }
        
        // 验证邮箱唯一性
        if (StringUtils.hasText(request.getEmail()) && isEmailExists(request.getEmail(), null)) {
            throw new BadRequestException("邮箱已存在: " + request.getEmail());
        }
        
        // 验证手机号唯一性
        if (StringUtils.hasText(request.getPhone()) && isPhoneExists(request.getPhone(), null)) {
            throw new BadRequestException("手机号已存在: " + request.getPhone());
        }
        
        // 验证部门存在性
        if (request.getDeptId() != null && departmentService.getById(request.getDeptId()) == null) {
            throw new BadRequestException("部门不存在: " + request.getDeptId());
        }
        
        // 创建用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(SaSecureUtil.md5(request.getPassword()));
        user.setNickname(request.getNickname());
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setDeptId(request.getDeptId());
        user.setRemark(request.getRemark());
        user.setStatus(1); // 默认启用
        user.setAccountLocked(false);
        user.setFailedLoginAttempts(0);
        
        user.setPasswordUpdateTime(LocalDateTime.now());
        
        userMapper.insert(user);
        
        // 分配角色
        if (!CollectionUtils.isEmpty(request.getRoleIds())) {
            assignRolesToUser(user.getId(), request.getRoleIds());
        }
        
        return user.getId();
    }

    @Override
    @Transactional
    public void updateUser(UpdateUserRequest request) {
        User existingUser = userMapper.selectById(request.getId());
        if (existingUser == null) {
            throw new ResourceNotFoundException("用户不存在: " + request.getId());
        }
        
        // 验证邮箱唯一性
        if (StringUtils.hasText(request.getEmail()) && isEmailExists(request.getEmail(), request.getId())) {
            throw new BadRequestException("邮箱已存在: " + request.getEmail());
        }
        
        // 验证手机号唯一性
        if (StringUtils.hasText(request.getPhone()) && isPhoneExists(request.getPhone(), request.getId())) {
            throw new BadRequestException("手机号已存在: " + request.getPhone());
        }
        
        // 验证部门存在性
        if (request.getDeptId() != null && departmentService.getById(request.getDeptId()) == null) {
            throw new BadRequestException("部门不存在: " + request.getDeptId());
        }
        
        // 更新用户信息
        User user = new User();
        user.setId(request.getId());
        user.setNickname(request.getNickname());
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setDeptId(request.getDeptId());
        user.setRemark(request.getRemark());
        user.setStatus(request.getStatus());
        
        userMapper.updateById(user);
    }

    @Override
    @Transactional
    public void deleteUser(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在: " + userId);
        }
        
        // 删除用户角色关联
        LambdaQueryWrapper<UserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserRole::getUserId, userId);
        userRoleMapper.delete(wrapper);
        
        // 删除用户
        userMapper.deleteById(userId);
    }

    @Override
    @Transactional
    public void batchDeleteUsers(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        
        // 删除用户角色关联
        LambdaQueryWrapper<UserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(UserRole::getUserId, userIds);
        userRoleMapper.delete(wrapper);
        
        // 批量删除用户
        userMapper.deleteBatchIds(userIds);
    }

    @Override
    @Transactional
    public void resetPassword(ResetPasswordRequest request) {
        User user = userMapper.selectById(request.getUserId());
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在: " + request.getUserId());
        }
        
        User updateUser = new User();
        updateUser.setId(request.getUserId());
        updateUser.setPassword(SaSecureUtil.md5(request.getNewPassword()));
        updateUser.setPasswordUpdateTime(LocalDateTime.now());
        updateUser.setFailedLoginAttempts(0); // 重置失败次数
        
        userMapper.updateById(updateUser);
    }

    @Override
    @Transactional
    public void changePassword(ChangePasswordRequest request) {
        User user = userMapper.selectById(request.getUserId());
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在: " + request.getUserId());
        }

        // 验证新密码和确认密码是否一致
        if (!request.getNewPassword().equals(request.getConfirmPassword())) {
            throw new IllegalArgumentException("新密码和确认密码不一致");
        }

        // 验证原密码是否正确
        String encryptedOldPassword = SaSecureUtil.md5(request.getOldPassword());
        if (!encryptedOldPassword.equals(user.getPassword())) {
            throw new IllegalArgumentException("原密码错误");
        }

        // 检查新密码是否与原密码相同
        String encryptedNewPassword = SaSecureUtil.md5(request.getNewPassword());
        if (encryptedNewPassword.equals(user.getPassword())) {
            throw new IllegalArgumentException("新密码不能与原密码相同");
        }

        User updateUser = new User();
        updateUser.setId(request.getUserId());
        updateUser.setPassword(encryptedNewPassword);
        updateUser.setPasswordUpdateTime(LocalDateTime.now());
        updateUser.setFailedLoginAttempts(0); // 重置失败次数

        userMapper.updateById(updateUser);
    }

    @Override
    @Transactional
    public void assignRoles(AssignRoleRequest request) {
        User user = userMapper.selectById(request.getUserId());
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在: " + request.getUserId());
        }
        
        assignRolesToUser(request.getUserId(), request.getRoleIds());
    }

    @Override
    @Transactional
    public void toggleUserStatus(ToggleStatusRequest request) {
        User user = userMapper.selectById(request.getUserId());
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在: " + request.getUserId());
        }
        
        User updateUser = new User();
        updateUser.setId(request.getUserId());
        updateUser.setStatus(request.getStatus());
        
        userMapper.updateById(updateUser);
    }

    @Override
    @Transactional
    public void lockUser(Long userId, String reason) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在: " + userId);
        }
        
        User updateUser = new User();
        updateUser.setId(userId);
        updateUser.setAccountLocked(true);
        updateUser.setLockTime(LocalDateTime.now());
        
        userMapper.updateById(updateUser);
    }

    @Override
    @Transactional
    public void unlockUser(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在: " + userId);
        }
        
        User updateUser = new User();
        updateUser.setId(userId);
        updateUser.setAccountLocked(false);
        updateUser.setLockTime(null);
        updateUser.setFailedLoginAttempts(0);
        
        userMapper.updateById(updateUser);
    }

    @Override
    public boolean isUsernameExists(String username) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getUsername, username);
        return userMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean isEmailExists(String email, Long excludeUserId) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getEmail, email);
        if (excludeUserId != null) {
            wrapper.ne(User::getId, excludeUserId);
        }
        return userMapper.selectCount(wrapper) > 0;
    }

    @Override
    public boolean isPhoneExists(String phone, Long excludeUserId) {
        if (!StringUtils.hasText(phone)) {
            return false;
        }
        
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getPhone, phone);
        if (excludeUserId != null) {
            wrapper.ne(User::getId, excludeUserId);
        }
        return userMapper.selectCount(wrapper) > 0;
    }

    @Override
    public List<RoleResponse> getUserRoles(Long userId) {
        List<Role> roles = roleService.findRolesByUserId(userId);
        return roles.stream()
                .map(this::convertToRoleResponse)
                .collect(Collectors.toList());
    }

    @Override
    public List<UserResponse> exportUsers(UserQueryRequest request) {
        // 设置一个较大的页面大小用于导出
        request.setPageSize(10000);
        IPage<UserResponse> page = getUserPage(request);
        return page.getRecords();
    }

    @Override
    @Transactional
    public ImportResult importUsers(List<CreateUserRequest> users) {
        ImportResult result = new ImportResult();
        result.setTotalCount(users.size());
        result.setErrorMessages(new ArrayList<>());
        
        int successCount = 0;
        for (CreateUserRequest user : users) {
            try {
                createUser(user);
                successCount++;
            } catch (Exception e) {
                result.getErrorMessages().add("用户 " + user.getUsername() + " 导入失败: " + e.getMessage());
            }
        }
        
        result.setSuccessCount(successCount);
        result.setFailureCount(users.size() - successCount);
        
        return result;
    }

    /**
     * 为用户分配角色
     */
    private void assignRolesToUser(Long userId, List<Long> roleIds) {
        // 删除现有角色关联
        LambdaQueryWrapper<UserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserRole::getUserId, userId);
        userRoleMapper.delete(wrapper);
        
        // 添加新的角色关联
        if (!CollectionUtils.isEmpty(roleIds)) {
            List<UserRole> userRoles = roleIds.stream()
                    .map(roleId -> new UserRole(userId, roleId))
                    .collect(Collectors.toList());
            
            for (UserRole userRole : userRoles) {
                userRoleMapper.insert(userRole);
            }
        }
    }

    /**
     * 转换为用户响应DTO
     */
    private UserResponse convertToUserResponse(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .username(user.getUsername())
                .nickname(user.getNickname())
                .email(user.getEmail())
                .phone(user.getPhone())
                .status(user.getStatus())
                .deptId(user.getDeptId())
                .remark(user.getRemark())
                .avatar(user.getAvatar())
                .lastLoginTime(user.getLastLoginTime())
                .lastLoginIp(user.getLastLoginIp())
                
                .accountLocked(user.getAccountLocked())
                .createTime(user.getCreateTime())
                .updateTime(user.getUpdateTime())
                .build();
    }

    /**
     * 转换为角色响应DTO
     */
    private RoleResponse convertToRoleResponse(Role role) {
        return RoleResponse.builder()
                .id(role.getId())
                .roleName(role.getRoleName())
                .roleCode(role.getRoleCode())
                .description(role.getDescription())
                .status(role.getStatus())
                .build();
    }
}
