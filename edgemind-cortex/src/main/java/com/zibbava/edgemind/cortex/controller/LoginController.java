package com.zibbava.edgemind.cortex.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.zibbava.edgemind.cortex.common.ApiResponse;
import com.zibbava.edgemind.cortex.dto.LoginRequest;
import com.zibbava.edgemind.cortex.entity.User;
import com.zibbava.edgemind.cortex.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 登录控制器
 * 
 * <AUTHOR> Team
 * @since 2025-06-28
 */
@Slf4j
@Controller
@RequiredArgsConstructor
public class LoginController {

    private final UserService userService;
    private final PasswordEncoder passwordEncoder;
    
    // 登录失败次数记录 (IP -> 失败次数)
    private final ConcurrentHashMap<String, AtomicInteger> failedAttempts = new ConcurrentHashMap<>();
    
    // 账户锁定记录 (IP -> 锁定时间)
    private final ConcurrentHashMap<String, LocalDateTime> lockedAccounts = new ConcurrentHashMap<>();
    
    // 最大失败次数
    private static final int MAX_FAILED_ATTEMPTS = 5;
    
    // 锁定时间（分钟）
    private static final int LOCK_TIME_MINUTES = 15;

    /**
     * 显示登录页面
     */
    @GetMapping("/login")
    public String loginPage(HttpServletRequest request, Model model) {
        // 如果已经登录，重定向到首页
        if (StpUtil.isLogin()) {
            String redirect = request.getParameter("redirect");
            if (StringUtils.hasText(redirect)) {
                return "redirect:" + redirect;
            }
            return "redirect:/system/user/content";
        }
        
        // 添加重定向参数到模型
        String redirect = request.getParameter("redirect");
        if (StringUtils.hasText(redirect)) {
            model.addAttribute("redirect", redirect);
        }
        
        return "login";
    }

    /**
     * 处理登录请求
     */
    @PostMapping("/login")
    @ResponseBody
    public ApiResponse<String> login(@RequestBody @Validated LoginRequest loginRequest, 
                                   HttpServletRequest request, 
                                   HttpServletResponse response) {
        
        String clientIp = getClientIp(request);
        
        try {
            // 检查IP是否被锁定
            if (isIpLocked(clientIp)) {
                log.warn("IP {} 尝试登录但已被锁定", clientIp);
                return ApiResponse.error("账户已被锁定，请" + LOCK_TIME_MINUTES + "分钟后重试");
            }
            
            // 验证用户名和密码
            User user = validateUser(loginRequest.getUsername(), loginRequest.getPassword());
            if (user == null) {
                // 记录失败次数
                recordFailedAttempt(clientIp);
                log.warn("用户 {} 登录失败，IP: {}", loginRequest.getUsername(), clientIp);
                return ApiResponse.error("用户名或密码错误");
            }
            
            // 检查用户状态
            if (user.getStatus() == 0) {
                log.warn("用户 {} 已被禁用，IP: {}", loginRequest.getUsername(), clientIp);
                return ApiResponse.error("账户已被禁用，请联系管理员");
            }
            
            // 登录成功，清除失败记录
            clearFailedAttempts(clientIp);
            
            // Sa-Token 登录
            StpUtil.login(user.getId());
            
            // 设置记住我
            if (loginRequest.getRememberMe() != null && loginRequest.getRememberMe()) {
                StpUtil.getTokenSession().setTimeout(7 * 24 * 60 * 60); // 7天
            }
            
            // 更新用户最后登录时间
            userService.updateLastLoginTime(user.getId());
            
            log.info("用户 {} 登录成功，IP: {}", user.getUsername(), clientIp);
            
            return ApiResponse.success("登录成功");
            
        } catch (Exception e) {
            log.error("登录处理异常，IP: {}", clientIp, e);
            return ApiResponse.error("登录失败，请稍后重试");
        }
    }

    /**
     * 登出
     */
    @PostMapping("/logout")
    @ResponseBody
    public ApiResponse<String> logout(HttpServletRequest request) {
        try {
            String clientIp = getClientIp(request);
            Object loginId = StpUtil.getLoginIdDefaultNull();
            
            StpUtil.logout();
            
            log.info("用户 {} 登出成功，IP: {}", loginId, clientIp);
            return ApiResponse.success("登出成功");
            
        } catch (Exception e) {
            log.error("登出处理异常", e);
            return ApiResponse.error("登出失败");
        }
    }

    /**
     * 检查登录状态
     */
    @GetMapping("/api/auth/status")
    @ResponseBody
    public ApiResponse<Object> checkLoginStatus() {
        try {
            if (StpUtil.isLogin()) {
                Object loginId = StpUtil.getLoginId();
                User user = userService.getById((Long) loginId);
                
                if (user != null && user.getStatus() == 1) {
                    return ApiResponse.success("已登录").data(user);
                } else {
                    // 用户不存在或已被禁用，强制登出
                    StpUtil.logout();
                    return ApiResponse.error("用户状态异常，请重新登录");
                }
            } else {
                return ApiResponse.error("未登录");
            }
        } catch (Exception e) {
            log.error("检查登录状态异常", e);
            return ApiResponse.error("检查登录状态失败");
        }
    }

    /**
     * 验证用户
     */
    private User validateUser(String username, String password) {
        try {
            User user = userService.findByUsername(username);
            if (user != null && passwordEncoder.matches(password, user.getPassword())) {
                return user;
            }
            return null;
        } catch (Exception e) {
            log.error("验证用户异常: {}", username, e);
            return null;
        }
    }

    /**
     * 检查IP是否被锁定
     */
    private boolean isIpLocked(String ip) {
        LocalDateTime lockTime = lockedAccounts.get(ip);
        if (lockTime != null) {
            if (LocalDateTime.now().isBefore(lockTime.plusMinutes(LOCK_TIME_MINUTES))) {
                return true;
            } else {
                // 锁定时间已过，清除记录
                lockedAccounts.remove(ip);
                failedAttempts.remove(ip);
            }
        }
        return false;
    }

    /**
     * 记录失败次数
     */
    private void recordFailedAttempt(String ip) {
        AtomicInteger attempts = failedAttempts.computeIfAbsent(ip, k -> new AtomicInteger(0));
        int currentAttempts = attempts.incrementAndGet();
        
        if (currentAttempts >= MAX_FAILED_ATTEMPTS) {
            // 锁定账户
            lockedAccounts.put(ip, LocalDateTime.now());
            log.warn("IP {} 登录失败次数达到 {}，已锁定 {} 分钟", ip, currentAttempts, LOCK_TIME_MINUTES);
        }
    }

    /**
     * 清除失败记录
     */
    private void clearFailedAttempts(String ip) {
        failedAttempts.remove(ip);
        lockedAccounts.remove(ip);
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
