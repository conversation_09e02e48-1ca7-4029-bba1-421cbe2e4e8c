package com.zibbava.edgemind.cortex.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 安全配置
 * 
 * <AUTHOR> Team
 * @since 2025-06-28
 */
@Configuration
public class SecurityConfig {

    /**
     * 密码加密器
     * 使用BCrypt算法，强度为12
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12);
    }
}
